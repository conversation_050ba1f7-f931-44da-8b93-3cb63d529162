/*
 * @Author: xie_sm
 * @Date: 2022-02-28 16:07:02
 * @LastEditors: xie_sm
 * @LastEditTime: 2023-03-29 09:25:56
 * @FilePath: \mobile-template\vue.config.js
 * @Description:
 *
 */
/* eslint-disable no-param-reassign */
const path = require('path')
const CompressionPlugin = require("compression-webpack-plugin");
const {BundleAnalyzerPlugin} = require('webpack-bundle-analyzer')
const defaultSettings = require('./src/config')

const port = '8088'
const isProduction = process.env.NODE_ENV !== 'development'
// const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;
// 默认打包时移除console和debugger，调试时可关闭
const removeProdDebug = true

// 默认不开启代码体积可视化分析工具
const enableAnalyzer = false

function resolve(dir) {
  return path.join(__dirname, dir)
}

function recursiveIssuer(m) {
  if (m.issuer) {
    return recursiveIssuer(m.issuer);
  } else if (m.name) {
    return m.name;
  } else {
    return false;
  }
}

const fs = require('fs')

function cb(err) {
  if (err) {
    console.info('🚀🚀', '写入文件失败 -->', err, `<-- plugin.js/`)
  } else {
    console.info('🚀🚀', '版本号写入成功 -->', `<-- vue.config.js/`)
  }
}

class VersionIterator {
  apply(compiler) {
    compiler.hooks.run.tapAsync('rewrite version.json', (params, callback) => {
      // rewrite version.json
      const filePath = './public/static/version.json'
      const isFileExist = fs.existsSync(filePath)
      console.info('🚀🚀', '版本文件是否存在： -->', isFileExist, `<-- vue.config.js/`)
      if (isFileExist) {
        const content = fs.readFileSync(filePath, 'utf-8')
        const json = JSON.parse(content)
        console.info('🚀🚀', '当前版本号： -->', json.version, `<-- vue.config.js/`)
        json.version += 1
        fs.writeFileSync(filePath, JSON.stringify(json), cb)
      } else {
        const content = JSON.stringify({version: 1})
        fs.writeFile(filePath, content, cb)
      }
      callback()
    })
  }
}

module.exports = {
  // 开启eslint检查
  parallel: false,
  lintOnSave: true,
  devServer: {
    https: true,
    port,
    proxy: defaultSettings.proxy,
    // allowedHosts: [
    //   '42ee-216-24-186-149.ngrok-free.app', // 允许访问的域名地址，即花生壳内网穿透的地址
    //   '.42ee-216-24-186-149.ngrok-free.app'   // .是二级域名的通配符
    // ],
    disableHostCheck: true
  },
  publicPath: './',
  // outputDir: '../www',
  assetsDir: '',
  // 不需要生产环境的 source map，开启设置为true
  productionSourceMap: false,
  configureWebpack: config => {
    return {
      // 配置别名
      resolve: {
        alias: {
          '@': path.join(__dirname, './src'),
          '@modules': path.join(__dirname, './src/modules'),
          '@components': path.join(__dirname, './src/components'),
          '@store': path.join(__dirname, './src/store'),
          '@assets': path.join(__dirname, './src/assets'),
          '@utils': path.join(__dirname, './src/utils'),
          '@common': path.join(__dirname, './src/common'),
        },
      },
      module: {},
      // 可查看webpack官网了解详细参数说明，
      // https://v4.webpack.js.org/plugins/split-chunks-plugin/#optimizationsplitchunks
      optimization: (function () {
        return isProduction
          ? {
            minimize: true,
            runtimeChunk: 'single',
            splitChunks: {
              chunks: 'all',
              minSize: 30000,
              maxSize: 250000,
              minChunks: 5,
              maxAsyncRequests: 5,
              maxInitialRequests: 5,
              enforceSizeThreshold: 50000,
            },
          }
          : {}
      })(),
      plugins: [
        new CompressionPlugin(isProduction ? {
          filename: '[path].gz[query]',
          algorithm: 'gzip',
          test: new RegExp('\\.(' + ['js', 'css'].join('|') + ')$'),
          threshold: 10240,
          minRatio: 0.8
        } : {}),

      ]
    }
  },
  chainWebpack: (config) => {
    // 移除prefetch
    config.plugins.delete("prefetch");
    // 将css样式代码合并在一起
    // const splitOptions = config.optimization.get('splitChunks')
    // splitOptions.cacheGroups.appStyles = {
    //   name: 'styles',
    //   test: (m, c, entry = 'app') => m.constructor.name === 'CssModule' && recursiveIssuer(m) === entry,
    //   chunks: 'all',
    //   minChunks: 1,
    //   enforce: true
    // }
    // config.optimization.splitChunks(splitOptions)
    // 解决开发环境debugger语句定位异常
    if (!isProduction) {
      config.devtool('eval-source-map')
    }
    // 修改html中的title值
    config.plugin('html').tap((args) => {
      args[0].title = '华智车管'
      return args
    })
    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .tap((options) => Object.assign(options, {limit: 10240}))
    // 压缩静态资源文件
    if (isProduction) {
      // ios端暂不支持gzip格式，先屏蔽
      // config.plugin("compressionPlugin").use(
      //   new CompressionPlugin({
      //     filename: "[path].gz[query]",
      //     algorithm: "gzip",
      //     test: productionGzipExtensions,
      //     threshold: 10240,
      //     minRatio: 0.8,
      //     deleteOriginalAssets: true,
      //   })
      // );
      if (removeProdDebug) {
        config.optimization.minimizer('terser').tap((args) => {
          // 去除生产环境console和debugger
          args[0].terserOptions.compress.drop_console = false
          args[0].terserOptions.compress.drop_debugger = true
          return args
        })
      }

      if (enableAnalyzer) {
        // 可视化分析工具，打包完成后会自行在浏览器窗口打开结果
        config.plugin('webpack-bundle-analyzer').use(BundleAnalyzerPlugin)
      }
    }
    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/assets/svg')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svg')) //处理svg目录
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end()
  },
  css: {
    // 屏蔽打包时组件样式的顺序警告——https://github.com/youzan/vant/issues/6486
    extract: {
      ignoreOrder: true,
    },
  },
}
