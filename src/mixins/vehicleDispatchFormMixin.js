import { getWayPoints, sensitiveDetection } from '@/api/carApply'
import { mapActions, mapState } from 'vuex'
import {
  feeConfigs,
  formatNumber,
  formTabs,
  getTaskNumber,
  UserTask_0,
  UserTask_100,
  UserTask_200,
  UserTask_300,
  UserTask_400,
  UserTask_500,
} from '@utils/constants'
import platform from '@/mixins/platform'
import dayjs from 'dayjs'
import { Toast } from 'fawkes-mobile-lib'
import { queryTips, tipsMap } from '@utils/notifyContentUtils'
import { isDefined } from '@utils/types'
import { uuid } from '@utils/util'
import Sortable from 'sortablejs'
import { getDriverInfoByCar } from '@modules/ProjectCar/ProjectPortal/CarManage/api'
import { getCar } from '@modules/ProjectCar/ProjectPortal/DriverManage/api'
import filedConfigMixin from '@/mixins/filedConfigMixin'
import { applyFieldLinkage } from '@modules/FormCenter/vehicleDispatch/config/linkageConfig'
import { TaskParamFactory } from '@modules/FormCenter/vehicleDispatch/config/TaskParamFactory'
import { createInitChain } from '@modules/FormCenter/vehicleDispatch/config/InitChain'
import { createInitContext } from '@modules/FormCenter/vehicleDispatch/config/initContextFactory'
import { getRulesModule } from '@modules/FormCenter/vehicleDispatch/config/ruleFactory'
import { ButtonKeys, TaskKeys } from '@store/modules/FormController/formFieldConfig'
import {
  clickFormRow,
  loadData,
  saveDrafts,
} from '@modules/FormCenter/vehicleDispatch/config/formDraft'

export default {
  mixins: [platform, filedConfigMixin],
  props: [
    'taskKey',
    'type',
    'bizId',
    'viewType',
    'currentButtonKey',
    'noAuth',
    'notify',
    'isCrossNodeReturn',
    'params'
  ],
  data() {
    return {
      modelKey: 'vehicleDispatch', // 流程图的key
      entityName: 'vehicleDispatch',
      service: {
        initial: '/vehicle-dispatch/vd/flow/detail', //初始化接口
        submit: '/vehicle-dispatch/vd/flow/commit', //提交接口
      },
      addressType: '', // 地址选择类型
      addressIndex: 0,
      mapShow: false, // 是否显示地图
      locationInfo: {
        longitude: '',
        latitude: '',
        name: '',
      },
      // otherField1List: [0, 5, 10, 60], // 提前量
      timeStr: '', // 预计时间
      distanceStr: '', // 预计距离
      activeNames: [], // 展开地图
      popupPerson: false, // 选人弹窗
      currApplyPerson: {},
      approveList1: [], // 审批领导列表
      initApproveList1: [], // 审批领导列表（初始化）[]
      approveList2: [], // 车辆调度员列表
      initApproveList2: [], // 车辆调度员列表（初始化）
      secondLevelUnitList: [], // 二级部门列表
      filteredSecondLevelUnitList: [], // 过滤后的二级部门列表
      popupShow: false, // 车辆选择弹窗
      popupDriver: false, // 司机选择弹窗
      isEnterCar: false, // 手动输入车辆信息
      isEnterDriver: false, // 手动输入司机信息
      vdCarInfo: { carNum: '', carType: '', carAge: '' },
      vdDriverInfo: { driverFullName: '', driverPhone: '', driverAge: '' },
      // xcForm: ['startAddress', 'endAddress', 'startTime', 'endTime', 'contacts', 'contactsPhone'], // 用于行程变更时，可选表单项
      xcForm: [
        'startAddress',
        'endAddress',
        'startTime',
        'endTime',
        'useCarTripType',
        'useCarJgTimeHour',
        'useCarJgTimeMinute',
      ], // 用于行程变更时，可选表单项
      modifycomment: '',
      formData: {
        remindTitle: '', // 提醒标题
        useCarMatter: '', // 用车事由
        flagBb: false, // 是否补报
        remark1: '', // 补充说明
        remark2: '', // 留言
        fyDeptName: '', // 用车费用归属部门
        fyDeptId: '', // 用车费用归属部门id
        startAddress: '', // 出发地点
        startAddressDetail: '', // 出发地详细地址
        startAddressSmx: '', // 出发地纬度
        startAddressSmy: '', // 出发地经度
        endAddress: '', // 目的地
        endAddressDetail: '', // 目的地详细地址
        endAddressSmx: '', // 目的地纬度
        endAddressSmy: '', // 目的地经度
        startTime: '', // 出车时间
        endTime: '', // 预计返回时间
        // otherField1: 5, // 提前量
        useCarPersonNum: 0, // 乘车人数
        vdAfUseCarPersonList: [], //
        delVdAfUseCarPersonList: [], // 删除的乘车人
        contacts: '', // 联系人
        contactsFullName: '', // 联系人
        contactsPhone: '', // 联系人电话
        contactsOpenId: '', // 联系人openId（飞书、微信才有这个）
        contactsResource: 1, // 联系人来源（1、飞书，2、微信，3、输入）
        task0UserName: '', // 申请人
        task0FullName: '', // 申请人
        task0Department: '', // 申请人部门名称
        task0TopDepartment: '', // 申请人顶级部门
        task0Time: '', // 用车申请时间
        task100UserName: '', // 审批领导
        task100FullName: '', // 审批领导
        task200UserName: '', // 车辆调度员
        task200FullName: '', // 车辆调度员
        task300FullName: '', // 司机
        task300UserName: '', // 司机
        task400FullName: '', // 司机
        task400UserName: '', // 司机
        task500FullName: '', // 车辆调度员
        task500UserName: '', // 车辆调度员
        carId: '', // 车辆id
        driverId: '', // 司机id
        startTime2: '', // 实际开始时间
        endTime2: '', // 实际结束时间
        km1: 0, // 出车前里程（公里）
        km2: 0, // 回车后里程（公里）
        cost1: 0, // 公路通行费（元
        cost1Type: '', // 公路通行费支付方式
        cost1Fj: '', // 公路通行费附件
        cost7: 0, // 桥、闸通行费
        cost7Fj: '', // 桥、闸通行费附件
        cost7Type: '', // 桥、闸通行费支付方式
        cost4: 0, // 车辆燃油费
        cost4Fj: '', // 燃油费附件
        cost4Type: '', // 燃油费支付方式
        cost4Field1: '', // 燃油标号
        other2: 0, // 加油量（升）
        cost8: 0, // 车辆清洗费
        cost8Fj: '', // 车辆清洗费附件
        cost9: 0, // 停车费
        cost9Fj: '', // 停车费附件
        cost9Type: '', // 停车费支付方式
        cost2: 0, // 住宿费
        cost2Fj: '', // 住宿费附件
        cost5: 0, // 其他费用（元）
        cost5Fj: '', // 其他费用附件
        cost6: 0, // 费用总计（元）
        remark3: '', // 备注
        attachment1: '', // 附件,
        attachment2: '', // 出车前里程拍摄图
        attachment3: '', // 出车后里程拍摄图
        comment: '', // 审批意见
        useCarNature: '', //用车性质
        useCarTripType: '', // 出车类型
        useCarJgTimeHour: 3, // 往返间隔小时
        useCarJgTimeMinute: 0, // 往返间隔分钟
        otherField2: '', // 航班号/车次
        useCarNatureStr: '',
        vdAfWaypointList: [], // 途经点列表
      },
      feeForm: {
        startTime2: '', // 实际开始时间
        endTime2: '', // 实际结束时间
        km1: 0, // 出车前里程（公里）
        km2: 0, // 回车后里程（公里）
        cost1: 0, // 公路通行费（元
        cost1Type: '', // 公路通行费支付方式
        cost1Fj: '', // 公路通行费附件
        cost7: 0, // 桥、闸通行费
        cost7Fj: '', // 桥、闸通行费附件
        cost7Type: '', // 桥、闸通行费支付方式
        cost4: 0, // 车辆燃油费
        cost4Fj: '', // 燃油费附件
        cost4Type: '', // 燃油费支付方式
        cost4Field1: '', // 燃油标号
        other2: 0, // 加油量（升）
        cost8: 0, // 车辆清洗费
        cost8Fj: '', // 车辆清洗费附件
        cost9: 0, // 停车费
        cost9Fj: '', // 停车费附件
        cost9Type: '', // 停车费支付方式
        cost2: 0, // 住宿费
        cost2Fj: '', // 住宿费附件
        cost5: 0, // 其他费用（元）
        cost5Fj: '', // 其他费用附件
        cost6: 0, // 费用总计（元）
        remark3: '', // 备注
        attachment1: '', // 附件,
        attachment2: '', // 出车前里程拍摄图
        attachment3: '', // 出车后里程拍摄图
      },
      feeConfigs,
      currentTab: '',
      tabs: [],
      notifyTitle: '',
      notifyTime: '',
      startAddrNotify: false,
      endAddrNotify: false,
      isSensitiveStartAddress: false, // 出发地包含敏感词汇
      isSensitiveEndAddress: false, // 目的地包含敏感词汇
      isSensitiveRemark: false, // 补充说明里包含敏感词汇
      showRate: false,
      cameraShow: false,
      sensitiveContent: {
        startNotify: '',
        endNotify: '',
      },
      wayPointsSensitive: [],
      useCarMatterList: [],
      initUseCarMatterList: [],
      historyUseCarNature: null,
      addrTotalHeight: 154,
      bracketHeight: 81,
      bracketTop: 20,
      addressDontCheck: false,
      // 初始化完毕
      initFinish: false,
      // 空途经点对象
      emptyViaAddr: {
        id: null,
        formId: '',
        address: '',
        addressSmx: '',
        addressSmy: '',
        uniId: '',
      },
      formRules: {},
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['applyResource', 'userInfo', 'openId', 'portal', 'vdProjectInfo']),
    taskKey300() {
      return TaskKeys.TASK300
    },
    showWayPoints() {
      return this.isApply || this.isReSubmit || this.isXcModify || this.isFee || this.isModifyAll
    },
    showJgFormItem() {
      // 当出车形式选择往返时，展示间隔时长表单项, 以及预计返回时间
      return this.formData.useCarTripType === 200
    },
    // 乘车申请人表单项样式
    applierCols() {
      if (this.$route.name === 'Application') {
        return {
          span: [3, 9, 10, 2],
          offset: [0, 0, 0, 0],
        }
      } else {
        return {
          span: [24, 9, 12, 3],
          offset: [0, 0, 0, 0],
        }
      }
    },
    showNotify() {
      const startTime = this.formData.startTime
      if (!startTime) return false
      const timeStr = startTime.replace(/-/g, '/')
      const selectedDate = new Date(timeStr)
      const hours = selectedDate.getHours()
      if (hours >= 23 || hours < 5) {
        return true
      }
      return false
    },
    approvalAdvice() {
      // 如果在司机确认流程
      return this.taskKey === UserTask_300
        ? '请输入审批意见，如需退回请在此输入退回原因'
        : '请输入审批意见'
    },
    // 当前表单是否在抽屉框里打开
    isDrawer() {
      return this.viewType === 'drawer'
    },
    // 在司机确认流程，可以显示行程信息并填报
    isDriverConfirm() {
      return this.type === 'execute' && this.taskKey === UserTask_300 && !this.isModify
    },
    // 选择行程信息变更
    isXcModify() {
      return this.$route.query && this.currentButtonKey === 'XC_FORM_MODIFY'
    },
    // 选择车辆/司机变更
    isXcDriverCarModify() {
      return this.currentButtonKey === 'XC_DRIVER_CAR_MODIFY'
    },
    // 租车公司变更
    isCarModify() {
      return this.currentButtonKey === 'XC_CAR_COMP_MODIFY'
    },
    // 修改，车辆调度员可以更改表单所有内容
    isModifyAll() {
      return this.currentButtonKey === 'FORM_MODIFY_ADMIN'
    },
    isModify() {
      return this.isXcModify || this.isXcDriverCarModify || this.isCarModify
    },
    showApprove() {
      return this.type === 'execute' && !this.isModify
    },
    alertText() {
      return this.formData.remindTitle.split('\n')
    },
    // 提交按钮文字
    submitText() {
      const name = {
        UserTask_0: '提交',
        UserTask_100: '同意',
        UserTask_200: '同意',
        UserTask_300: '确认行程开始',
        UserTask_400: '提交',
        UserTask_500: '确认费用',
      }
      return name[this.taskKey]
    },
    formName() {
      const name = {
        UserTask_0: '用车申请',
        UserTask_100: '领导审批',
        UserTask_200: '车辆调度员派车',
        UserTask_300: '司机确认',
        UserTask_400: '司机行车日志填报',
        UserTask_500: '车辆调度员审批',
      }
      const modify = {
        XC_FORM_MODIFY: '行程信息变更',
        XC_DRIVER_CAR_MODIFY: '司机/车辆信息变更',
        XC_CAR_COMP_MODIFY: '租车公司变更',
        FORM_MODIFY_ADMIN: '修改',
      }
      const modified = this.isModify || this.isModifyAll
      return modified
        ? modify[this.currentButtonKey]
        : this.type === 'view'
        ? '详情'
        : this.$route.params.taskName || name[this.taskKey]
      // if (this.type === 'view') {
      //   return '详情'
      // } else {
      //   return this.$route.params.taskName || name[this.taskKey];
      // }
    },
    // 用车申请流程
    isApply() {
      return this.type === 'add'
    },
    isAdd() {
      // 用车申请表单页
      return this.isApply || this.isReSubmit
    },
    // 重新填写申请流程
    isReSubmit() {
      return this.type === 'execute' && this.taskKey === UserTask_0
    },
    // 费用填报流程
    isFee() {
      return this.type === 'execute' && this.taskKey === UserTask_400 && !this.currentButtonKey
    },
    // 控制流程表单的显隐，可否编辑
    flowConfig() {
      const config = {}
      let list = this.currentFieldConfig
      const { required = [], disabled = [] } = list
      const requiredSet = new Set(required)
      const disabledSet = new Set(disabled)
      Object.keys(this.formData).forEach((key) => {
        if (requiredSet.has(key)) {
          config[key] = ''
        }
        if (disabledSet.has(key)) {
          config[key] = 'readonly'
        }
        // if (this.type === 'view' || this.type === 'execute') {
        //   const xc = this.xcForm;
        //   // const xcDriverCar = ['realCarType', 'carCompanyName', 'carCompanyName'];
        //   // const xcDriverCar = [];
        //   // const xcDriverCar = Object.keys(this.vdCarInfo).concat(Object.keys(this.vdDriverInfo));
        //   const xcDriverCar =[];
        //   const feeForm = Object.keys(this.feeForm);
        //   const zb = ['task200UserName'];
        //   // 暂存或者退回到申请人的时候，非编辑（行程信息变更 、车辆/司机信息变更）||
        //   // 行程信息变更 ||
        //   // 车辆/司机信息变更 ||
        //   // 审批的时候,处于审批领导审批时，需选择车辆调度员 ||
        //   // 审批的时候, 当处于费用填报时 ||
        //   // 审批的时候，处于司机确认时，需填写出车前里程和实际开始时间
        //   // 费用审批的时时候
        //   // 需要将费用表单置为可选
        //   config[key] = ((this.isReSubmit && !this.isModify) ||
        //     (this.isXcModify && xc.includes(key)) ||
        //     (this.isXcDriverCarModify && xcDriverCar.includes(key)) ||
        //     (this.type === 'execute' && this.taskKey === UserTask_100 && zb.includes(key)) ||
        //     (!this.isModify && this.type === 'execute' && this.taskKey === UserTask_400 && feeForm.includes(key))) ?
        //     '' : 'readonly'
        //   if (this.type === 'execute' && this.taskKey === UserTask_300) {
        //     config['km1'] = '';
        //     config['startTime2'] = ''
        //   }
        //   // 费用填报的时候，可以修改地址
        //   if (this.isFee) {
        //     config['startAddress'] = '';
        //     config['endAddress'] = '';
        //   }
        //   if (this.isEditCar) {
        //     config['carNum'] = '';
        //     config['driverFullName'] = ''
        //   }
        //   // 修改时，除了【是否补报】，【领导】，【车辆调度员】之外都能改
        //   if (this.isModifyAll) {
        //     if (key === 'flagBb' || key === 'task100UserName' || key === 'task200UserName') {
        //       config[key] = 'readonly'
        //     } else {
        //       config[key] = '';
        //     }
        //   }
        // } else {
        //   config[key] = '';
        // }

        // 申请 || 退回到申请人
        // if (this.isApply || this.isReSubmit) {
        //   config[key] = ''
        // } else {
        //   config[key] = 'readonly';
        // }
        // if (this.type === 'execute') {
        //   // 处于审批领导审批时，需选择车辆调度员
        //   if (this.taskKey === UserTask_100) {
        //     config['task200UserName'] = ''
        //   } else if (this.taskKey === UserTask_400) { // 当处于费用填报时，需要将费用表单置为可选
        //     Object.keys(this.feeForm).forEach(key => {
        //       config[key] = ''
        //     })
        //   }

        // // 行程信息变更
        // if (this.isXcModify) {
        //   this.xcForm.forEach(key => {
        //     config[key] = ''
        //   })
        // }
        //
        // // 车辆/司机信息变更
        // if (this.isXcDriverCarModify) {
        //   Object.keys(this.vdCarInfo).concat(Object.keys(this.vdDriverInfo)).forEach(key => {
        //     config[key] = ''
        //   })
        // }
      })
      return config
    },
    // 表单-补充说明-是否必选
    isRemark1Required() {
      // 当用车事由选择【维修保养】和【其他】时必选
      return (
        this.formData.useCarMatter === 400 ||
        this.formData.useCarMatter === 600 ||
        this.formData.useCarMatter === 800
      )
    },
    // 表单-出发地点-选项
    startAddresses() {
      return this.enums?.StartAddressEnums ?? []
    },
    // 表单-出发形式-选项
    useCarTripTypeOptions() {
      return this.enums?.UseCarTripTypeEnums ?? []
    },
    // 出车时间时间选择限制
    startPickerOptions() {
      const that = this
      return {
        disabledDate(time) {
          const btnKey = that.$store.state.FormController.formBtnKey
          // 补报和修改不限制时间
          if (that.formData.flagBb) {
            return false
          } else if (btnKey === ButtonKeys.FORM_MODIFY_ADMIN) {
            return false
          } else {
            return time.getTime() < Date.now() - 8.64e7
          }
        },
      }
    },
    currProjectName() {
      let projectName = this.formData.projectName
      if (projectName) {
        return projectName
      }
      return this.$route.params.projectName || this.portal.name
    },
    // 结束时间选择限制
    endPickerOptions() {
      const that = this
      return {
        disabledDate(time) {
          if (that.formData.startTime) {
            const d = new Date(new Date(that.formData.startTime).getTime()).setHours(0, 0, 0, 0)
            return time.getTime() < d
          }
          return true
        },
      }
    },
    // 出发地点和目的地是否都已选择
    isStartAndEndPointSelected() {
      return (
        this.formData.startAddressSmx &&
        this.formData.startAddressSmy &&
        this.formData.endAddressSmx &&
        this.formData.endAddressSmy
      )
    },
    // 表单-乘车人数-是否必选
    isUseCarPersonNumRequired() {
      // 当【用车事由】选择“维修保养”，“年检”，“加油”时可以为0，也就是不必做表单校验
      return [300, 400, 500].findIndex((item) => item === this.formData.useCarMatter) === -1
    },
    // 表单-添加乘车人按钮-显隐
    showAddPassengerButton() {
      // 显示乘车人按钮的场景：用车申请
      // 不显示乘车人按钮的场景：只读
      // v-if="(isAdd && isUser || (taskKey === 'UserTask_0' && type === 'draft')) && !(flowConfig.useCarPersonNum === 'readonly')"
      const showButton =
        this.taskKey === UserTask_0 ||
        this.type === 'add' ||
        this.type === 'draft' ||
        this.isModifyAll
      const hideButton = this.type === 'view'
      return showButton && !hideButton && !this.isXcModify
    },
    //表单-审批领导-表单项-禁用
    disableTask100UserFormItem() {
      // 表单限制为只读
      return this.flowConfig.task100UserName === 'readonly'
    },
    // 表单-车辆调度员-表单项-禁用
    disableTask200UserFormItem() {
      const d1 = this.taskKey === UserTask_0
      return !d1 || this.flowConfig.task200UserName === 'readonly'
    },
    // 表单-车辆调度员-表单项-显隐
    showTask200UserFormItem() {
      // 用车申请时，选择工地内部或选择补报则显示，其余情况不显示
      if (this.isApply || this.isReSubmit) {
        const isFlagBb = this.formData.flagBb
        return +this.formData.useCarMatter === 700 || isFlagBb
      }
      return this.isModify ? !!this.formData.task200UserName : true
    },
    // 行程信息变更的时候， 有车辆信息显示；非行程信息变更， 显示
    isXcCar() {
      return this.isXcModify ? !!this.vdCarInfo.carNum : true
    },
    isBbWhileApply() {
      return this.formData.flagBb && this.isAdd
    },
    isCar() {
      // 如果有taskKey, 表示流程没有结束，需要判断流程节点进行显影
      // 如果没有taskKey, 表示流程已经结束，需要判断是否是【修改】表单
      if (this.taskKey) {
        return (
          (getTaskNumber(this.taskKey) > 100 || //表单-车辆选择-表单项-当流程在审批领导审批后面时，都能显示
            this.isXcDriverCarModify || // 表单-车辆/司机信息变更-表单项-当流程在车辆/司机变更时，都能显示
            this.isBbWhileApply) && // 用车申请时，补报为是，则允许编辑车辆/司机信息
          //this.fromHistory 从历史记录进入
          this.isXcCar
        )
      } else {
        return this.isModifyAll
      }
    },
    // 表单-行车日志填报-表单项-显隐
    isLog() {
      // 如果有taskKey, 表示流程没有结束，需要判断流程节点进行显影
      // 如果没有taskKey, 表示流程已经结束，需要判断是否是【修改】表单
      if (this.taskKey) {
        return getTaskNumber(this.taskKey) > 300
      } else {
        return this.isModifyAll
      }
    },
    // 是否可以选择车辆
    isEditCar() {
      // 用车申请时，补报为是，则允许编辑车辆/司机信息
      if (this.isBbWhileApply) return true
      if (this.type === 'execute') {
        // 非行程信息变更
        // 1. 处于车辆调度员审批流程时可以编辑
        // 2. 处于车辆/司机变更时可以编辑
        // 3. 费用审批时可以编辑
        return (
          !this.isXcModify &&
          (this.taskKey === UserTask_200 || this.isXcDriverCarModify || this.isModifyAll)
        )
      }
      return false
    },
    // 行车总费用
    journeyCostTotal() {
      const total =
        formatNumber(this.formData.cost1) +
        formatNumber(this.formData.cost2) +
        formatNumber(this.formData.cost7) +
        formatNumber(this.formData.cost4) +
        formatNumber(this.formData.cost8) +
        formatNumber(this.formData.cost5) +
        formatNumber(this.formData.cost9)
      return total.toFixed(2)
    },
    currProjectNature() {
      let list = []
      let path = this.$route.path
      if (this.type === 'view' || path.includes('companyPortal')) {
        return this.enums.UseCarNatureEnums
      }

      let nature = this.vdProjectInfo.projectUseCarNature
      if (nature && (this.isApply || this.isReSubmit || this.isModifyAll)) {
        let natureList = nature.split(',').map((item) => Number(item))
        list = this.enums.UseCarNatureEnums.filter((item) => {
          return natureList.includes(item.key)
        })
      }
      if (this.historyUseCarNature && this.taskKey) {
        let find = this.enums.UseCarNatureEnums.find((item) => {
          return item.key == this.historyUseCarNature
        })
        if (find && !list.some((item) => item.key === find.key)) {
          list.push(find)
        }
      }
      return list
    },
    // 当出发地，途经地，目的地其中任意一个存在手动输入的地址，则需要提示路径规划功能不可用
    showTrackAlert() {
      // 判断依据：地址有值，但是没有经纬度信息
      const startAddressAlert =
        !this.formData.startAddressSmx &&
        !this.formData.startAddressSmy &&
        this.formData.startAddress
      const endAddressAlert =
        !this.formData.endAddressSmx && !this.formData.endAddressSmy && this.formData.endAddress
      const wayPointAlert = this.formData.vdAfWaypointList.some((item) => {
        return !item.addressSmx && !item.addressSmy && item.address
      })
      return startAddressAlert || endAddressAlert || wayPointAlert
    },
    vdUserInfo() {
      return this.$storage.getObject('user')
    },
  },
  methods: {
    ...mapActions('CarApply', ['getCurrentUser', 'getEnum']),
    handleFyDeptIdChange(item) {
      const dept = this.secondLevelUnitList.find((el) => el.id === item)
      if (dept) {
        this.formData.fyDeptName = dept.deptName
        this.clickFormRow()
      }
    },
    handleEndTimeChange(endTime) {
      if (endTime && this.formData.startTime && this.showWayPoints && this.showJgFormItem) {
        // 减去开始时间，得到时间差，并分别得到小时和分钟
        const timeDiff = dayjs(endTime).diff(dayjs(this.formData.startTime))
        const hours = Math.floor(timeDiff / (1000 * 60 * 60))
        const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
        // 赋值间隔时间
        this.formData.useCarJgTimeHour = hours
        this.formData.useCarJgTimeMinute = minutes
        if (this.isMobile) {
          this.jgTime = `${hours}:${minutes}`
        }
      }
    },
    getAfterTime(time, hour, minute) {
      const totalMinutes = Number(hour) * 60 + Number(minute)
      return new Date(dayjs(time).valueOf() + totalMinutes * 60 * 1000)
    },
    // 行车日志填报-支付方式选项
    getSelectOptions(prop) {
      const source = this.enums.ApplyFormFyTypeEnums
      switch (prop) {
        case 'cost4':
          return source.filter((item) => item.key === 100 || item.key === 300)
        case 'cost5':
          return []
        case 'cost8':
          return []
        case 'km1':
          return []
        case 'km2':
          return []
        default:
          return source.filter((item) => item.key === 100 || item.key === 200)
      }
    },
    queryTipsContent() {
      let projectId = this.$route.params.projectId || this.portal.id
      queryTips(projectId, tipsMap.dispatchTitle).then((res) => {
        if (res.data.tipContent) {
          this.notifyTitle = res.data.tipContent
        }
      })
      queryTips(projectId, tipsMap.dispatchTime).then((res) => {
        if (res.data.tipContent) {
          this.notifyTime = res.data.tipContent
        }
      })
    },
    handleTabClick(tab) {
      // 滚动到点击实例的位置
      this.$nextTick(() => {
        if (this.$refs[tab.name]) {
          const el = this.$refs[tab.name].$el
          el.scrollIntoView({ behavior: 'smooth' })
        }
      })
    },
    getEntityParam(formProcessParam) {
      const formData = this.handleFormData(JSON.parse(JSON.stringify(this.formData)))

      // 补充项目信息
      formData.projectId = this.$route.params.projectId || this.params?.projectId || this.portal.id
      formData.projectName = this.$route.params.projectName || this.params?.projectName || this.portal.name

      formData.startTime = formData.startTime
        ? dayjs(formData.startTime).format('YYYY-MM-DD HH:mm:00')
        : ''
      formData.endTime = formData.endTime
        ? dayjs(formData.endTime).format('YYYY-MM-DD HH:mm:00')
        : ''

      // 途经点
      // formData.vdAfWaypointList = this.formData.vdAfWaypointList;

      // 找到对应的处理器
      const handler = TaskParamFactory.createHandler(this)
      const finalFormData = handler.process(formData)

      const {
        vdAfUseCarPersonList,
        delVdAfUseCarPersonList,
        vdAfUcCompleteFormList,
        vdAfWaypointList,
        ...rest
      } = finalFormData

      return {
        vdApplyForm: rest,
        vdAfUseCarPersonList: (vdAfUseCarPersonList || []).map(({ currentKey, ...item }) => item),
        vdAfUcCompleteFormList: vdAfUcCompleteFormList || [],
        vdAfWaypointList: vdAfWaypointList || [],
        delVdAfUseCarPersonList: delVdAfUseCarPersonList || [],
        formProcessParam,
        modifycomment: this.isModify ? this.modifycomment : null,
      }
    },
    handleFormData(data) {
      return data
    },
    getPickerOptions(prop) {
      const that = this
      switch (prop) {
        case 'startTime2':
          return {
            disabledDate() {
              return false
            },
          }
        case 'endTime2':
          return {
            disabledDate(time) {
              if (that.formData.startTime2) {
                const d = new Date(new Date(that.formData.startTime2).getTime()).setHours(
                  0,
                  0,
                  0,
                  0
                )
                return time.getTime() < d
              }
              return true
            },
          }
      }
    },
    isDefined,
    kmValidator(value, rule, item) {
      // 当表单不是只读，而且key是回车后里程
      if (this.flowConfig[item.prop] !== 'readonly' && item.prop === 'km2') {
        return Number(this.formData.km1) < Number(value)
      } else {
        return true
      }
    },
    feeValidatorMobile(value, rule, item) {
      // 当表单项只读时，不进行校验了
      if (this.flowConfig[item.prop] === 'readonly') {
        return true
      } else {
        return isDefined(rule) && /^(?:[1-9]\d*(?:\.\d{1,2})?|0(?:\.\d{1,2})?)$/.test(value)
      }
    },
    feeValidator(rule, value, callback) {
      // 当表单项只读时，不进行校验了
      if (this.flowConfig[rule.field] === 'readonly') {
        callback()
      } else {
        if (rule.required) {
          // 实际开始时间和实际结束时间为字符串
          if (rule.field === 'startTime2' || rule.field === 'endTime2') {
            if (value) {
              callback()
            } else {
              callback(new Error('不能为空'))
            }
          } else if (rule.field === 'km2') {
            if (Number(this.formData.km1) >= Number(value)) {
              callback(new Error('回车后里程不能小于出车前里程'))
            } else {
              callback()
            }
          } else {
            // 其他值类型均为数字
            // 如果值为空，则校验不通过
            // 如果值为空，则校验不通过
            if (isDefined(value) && /^(?:[1-9]\d*(?:\.\d{1,2})?|0(?:\.\d{1,2})?)$/.test(value)) {
              callback()
            } else {
              callback(new Error('请输入有效的数字'))
            }
          }
        } else {
          callback()
        }
      }
    },
    phoneValidator(val, value, callback) {
      if (this.isMobile) {
        if (val) {
          return /^[1][23456789][0-9]{9}$/.test(val)
        }
        return true
      } else {
        if (/^[1][23456789][0-9]{9}$/.test(value)) {
          callback()
        } else {
          if (!value) {
            callback(new Error('请输入手机号'))
          } else {
            callback(new Error('请输入正确的手机格式'))
          }
        }
      }
    },
    // 选择联系人员
    handleSelectContacts(val, flag = false) {
      if (!flag) {
        this.popupPerson = false
      }
      val.ucPersonFullName && (this.formData.contacts = val.ucPersonFullName)
      val.ucPersonPhone && (this.formData.contactsPhone = val.ucPersonPhone) // 联系人电话
      val.ucPersonResource && (this.formData.contactsResource = val.ucPersonResource) // 联系人来源
      val.ucPersonOpenId && (this.formData.contactsOpenId = val.ucPersonOpenId) // 联系人openId（飞书、微信才有这个）
    },
    // 选择申请人员
    closePopupPerson(val, flag = false) {
      if (!flag) {
        this.popupPerson = false
      }
      const { currentKey } = val
      if (currentKey) {
        this.currApplyPerson = val
        // 找到当前行的索引
        const currentIndex = this.formData.vdAfUseCarPersonList.findIndex(
          (item) => item.currentKey === currentKey
        )
        if (currentIndex > -1) {
          const rawData = this.formData.vdAfUseCarPersonList[currentIndex]
          // 判断原数据是否有id属性，有则说明是需要编辑的，没有表示是需要新增的
          const newData = rawData.id ? { ...val, id: rawData.id } : val
          this.$set(this.formData.vdAfUseCarPersonList, currentIndex, newData)
          this.clickFormRow()
        }
      } else {
        // 没有currentKey, 就是在移动端选择联系人的时候触发
        this.handleSelectContacts(val)
      }
    },
    // 添加乘车人
    addPerson() {
      this.formData.vdAfUseCarPersonList.unshift({
        ucPersonUserName: '',
        ucPersonFullName: '',
        ucPersonPhone: '',
        currentKey: uuid(4, 8),
      })
      this.clickFormRow()
      this.generateFormRules()
    },
    // 减少乘车人
    minusPerson(deleteItem) {
      if (deleteItem) {
        const index = this.formData.vdAfUseCarPersonList.findIndex(
          (item) => item.currentKey === deleteItem.currentKey
        )
        // 如果有id，说明删除项已经在后端保存了，需要将id放到delVdAfUseCarPersonList中，以便后续删除
        // 如果没有id，说明删除项还没有保存，不需要做任何操作
        if (deleteItem.id) {
          this.formData.delVdAfUseCarPersonList.push(deleteItem)
        }
        this.formData.vdAfUseCarPersonList.splice(index, 1)
      }
      this.clickFormRow()
      this.generateFormRules()
    },
    useCarPersonValidator(rule, value, callback) {
      if (this.isMobile) {
        return Number(rule) > 0 || !this.isUseCarPersonNumRequired
      } else {
        if ((value && value > 0) || !this.isUseCarPersonNumRequired) {
          callback()
        } else {
          callback(new Error('请输入乘车人数'))
        }
      }
    },
    // 获取预计时长和距离
    getTripInfo(startPoint, endPoint) {
      let distanceStr, timeStr
      const obj = {
        origin: `${startPoint.x},${startPoint.y}`,
        destination: `${endPoint.x},${endPoint.y}`,
        extensions: 'base',
        output: 'json',
        key: process.env.VUE_APP_AMAP_WEB_KEY,
      }
      const params = Object.keys(obj)
        .map((key) => `${key}=${obj[key]}`)
        .join('&')
      const url = `https://restapi.amap.com/v3/direction/driving?${params}`
      return fetch(url)
        .then((res) => res.json())
        .then((res) => {
          const path = res.route ? res.route.paths[0] : { distance: 0, duration: 0 }
          distanceStr = `${(Number(path.distance) / 1000).toFixed(2)}公里`
          let str = ''
          let append = ''
          const duration = Number(path.duration)
          if (duration / 60 < 60) {
            append = `${(duration / 60).toFixed(2)}分钟`
          } else {
            append = `${(duration / 3600).toFixed(2)}小时`
          }
          timeStr = str + append
          return { timeStr, distanceStr }
        })
    },
    // 结束时间验证器
    endTimeValidator(val, value, callback) {
      const startTime = dayjs(this.formData.startTime).valueOf()
      const endTime = dayjs(this.formData.endTime).valueOf()
      if (this.isMobile) {
        // return startTime < endTime
        if (!this.formData.startTime || !this.formData.endTime) {
          return true
        }
        if (startTime > endTime) {
          return false
        } else {
          return startTime !== endTime
        }
      } else {
        if (!this.formData.startTime || !this.formData.endTime) {
          callback()
        } else if (startTime > endTime) {
          callback(new Error('返回时间不能早于出发时间'))
        } else if (startTime === endTime) {
          callback(new Error('返回时间和出发时间不能相同'))
        } else {
          callback()
        }
      }
    },
    handleAddressClear(type, index) {
      if (type === 'start') {
        this.$set(this.formData, 'startAddressSmx', '')
        this.$set(this.formData, 'startAddressSmy', '')
        this.$set(this.formData, 'startAddress', '')
      } else if (type === 'end') {
        this.$set(this.formData, 'endAddressSmx', '')
        this.$set(this.formData, 'endAddressSmy', '')
        this.$set(this.formData, 'endAddress', '')
      } else if (type === 'via') {
        this.$set(this.formData.vdAfWaypointList[index], 'addressSmx', '')
        this.$set(this.formData.vdAfWaypointList[index], 'addressSmy', '')
        this.$set(this.formData.vdAfWaypointList[index], 'address', '')

        let refsArray = this.$refs.viaPointHighlightRef
        let refInstance
        if (Array.isArray(refsArray)) {
          refInstance = refsArray[index] // 根据 index 访问对应的组件
        }
        if (!refInstance) {
          return
        }

        if (refInstance) {
          refInstance.clearHighlight('')
        }
      }
    },
    getLocation(type, index) {
      this.addressType = type
      this.addressIndex = index

      if (type === 'start') {
        this.locationInfo = {
          longitude: this.formData.startAddressSmx,
          latitude: this.formData.startAddressSmy,
          name: this.formData.startAddress,
        }
      } else if (type === 'end') {
        this.locationInfo = {
          longitude: this.formData.endAddressSmx,
          latitude: this.formData.endAddressSmy,
          name: this.formData.endAddress,
        }
      } else if (type === 'via') {
        let addr = this.formData.vdAfWaypointList[index]
        this.locationInfo = {
          longitude: addr.addressSmx,
          latitude: addr.addressSmy,
          name: addr.address,
        }
      }
      this.mapShow = true
      this.clickFormRow()
    },
    handleMapUpdate(poi) {
      if (this.addressType === 'end') {
        this.$set(this.formData, 'endAddressSmx', poi.longitude)
        this.$set(this.formData, 'endAddressSmy', poi.latitude)
        this.$set(this.formData, 'endAddress', poi.name)
      } else if (this.addressType === 'start') {
        this.$set(this.formData, 'startAddressSmx', poi.longitude)
        this.$set(this.formData, 'startAddressSmy', poi.latitude)
        this.$set(this.formData, 'startAddress', poi.name)
      } else if (this.addressType === 'via') {
        let data = {
          addressSmx: poi.longitude,
          addressSmy: poi.latitude,
          address: poi.name,
        }
        let currData = this.formData.vdAfWaypointList[this.addressIndex]
        // 途径点关键字识别
        this.$set(this.formData.vdAfWaypointList, this.addressIndex, Object.assign(currData, data))
        this.wayPointKeyCheck(this.addressIndex)
      }
    },
    getApplyUserInfo(data) {
      const {
        userFullName,
        userName,
        userPhone: phone,
        userDepName2: deptName,
        userDepName: topDeptName,
      } = data
      this.formData.task0UserName = userName
      this.formData.task0FullName = userFullName
      this.formData.task0Time = this.$dayjs().format('YYYY-MM-DD HH:mm:ss')
      this.formData.task0Department = deptName
      this.formData.task0TopDepartment = topDeptName
      this.formData.task0Phone = phone
      this.formData.task0Resource = this.applyResource

      this.formData.contacts = userFullName // 联系人
      this.formData.contactsPhone = phone // 联系人电话
      this.formData.contactsResource = this.userInfo.userType || 3 // 联系人来源
      this.formData.contactsOpenId = this.openId // 联系人openId（飞书、微信才有这个）
    },
    handleApprove1Change(val) {
      const item = this.approveList1.find((item) => item.userName === val)
      this.formData.task100FullName = item.userFullname
      this.clickFormRow()
    },
    handleApprove2Change(val) {
      const item = this.approveList2.find((item) => item.userName === val)
      this.formData.task200FullName = item.userFullname
      this.clickFormRow()
    },
    // 当用车事由发生切换时，需要清空审批人信息
    clearApprove() {
      // 【修改】按钮中，暂时不清空审批人
      if (this.isModifyAll) {
        return
      }
      this.formData.task100FullName = ''
      this.formData.task100UserName = ''
      this.formData.task200FullName = ''
      this.formData.task200UserName = ''
      this.clickFormRow()
    },
    // 打开车辆选择
    chooseCar() {
      this.popupShow = true
    },
    // 打开选择司机列表
    chooseCarDriver() {
      this.popupDriver = true
    },
    // 输入车辆信息
    changeCar() {
      this.isEnterCar = true
    },
    // 输入司机信息
    changeDriver() {
      this.isEnterDriver = true
      this.$forceUpdate()
    },
    setCarInfo(vdCarInfo) {
      if (vdCarInfo) {
        this.$set(this.formData, 'carId', vdCarInfo.id)
        this.$set(this.formData, 'carType', vdCarInfo.carType)
        this.$set(this.formData, 'carNum', vdCarInfo.carNum)
        this.$set(this.formData, 'seatCount', vdCarInfo.carSeatNum)
        this.$set(this.formData, 'carAge', +vdCarInfo.carAge || '')
      }
    },
    setDriverInfo(vdDriverInfo) {
      if (vdDriverInfo) {
        const { driverAge, driverFullName, driverPhone, driverUserName, id } = vdDriverInfo
        this.$set(this.formData, 'driverUserName', driverUserName) // 司机用户名
        this.$set(this.formData, 'driverFullName', driverFullName) // 司机姓名
        this.$set(this.formData, 'driverPhone', driverPhone) // 司机手机号
        this.$set(this.formData, 'driverAge', +driverAge || '') // 司机驾龄
        this.$set(this.formData, 'driverId', id) // 司机id

        this.$nextTick(() => {
          ;['driverFullName', 'driverPhone'].forEach((field) => {
            this.$refs.formRef.validateField(field)
          })
        })
      }
    },
    async closePopupCar(vdCarInfo, flag = false) {
      this.isEnterCar = false
      this.setCarInfo(vdCarInfo)
      // 通知司机选择组件：车辆选择完毕了
      if (!this.formData.driverId) {
        /*
        如果没有选择司机，那么需要调接口获取目前选择的车辆所绑定的司机
        如果选择了司机，则不做任何操作，允许用户自己选择绑定的司机
        * */
        try {
          const res = await getDriverInfoByCar(vdCarInfo.id)
          if (res.status) {
            const vdDriverInfo = res.data[vdCarInfo.id]
            this.setDriverInfo(vdDriverInfo)
          }
        } catch (e) {
          console.info('🚀🚀', 'e -->', e, `<-- vehicleDispatchFormMixin.js/closePopupCar`)
        }
      }
      // !flag && this.closePopupDriver(vdCarInfo, true);
      if (!flag) {
        this.popupShow = false
      }
      this.clickFormRow()
    },
    onConfirmCar() {
      this.$refs.carList && this.$refs.carList.onSubmit()
    },
    onConfirmDriver() {
      this.$refs.driverList && this.$refs.driverList.onSubmit()
    },
    // 选择司机
    closePopupDriver(val, flag = false) {
      if (!flag) {
        this.popupDriver = false
      }
      this.setDriverInfo(val)
      // 如果没有选择车辆，则根据当前司机去选择绑定车辆
      if (!this.formData.carId) {
        try {
          getCar({ driverIds: val.id }).then((res) => {
            if (res.status) {
              const vdCarInfo = res.data[val.id]
              // 只有车辆状态为【正常】才能带入
              if (vdCarInfo && vdCarInfo.carStatus === 100) {
                this.setCarInfo(vdCarInfo)
              }
            }
          })
        } catch (e) {
          console.info('🚀🚀', 'e -->', e, `<-- vehicleDispatchFormMixin.js/closePopupDriver`)
        }
      }
      // !flag && this.closePopupCar(val, true);
      this.isEnterDriver = false
      this.clickFormRow()
    },
    formatPhoneNumber(item, name = 'ucPersonPhone') {
      const numberList = item[name].toString().split('')
      // 电话号码按照3-4-4格式显示
      // 添加第一个横杠
      numberList.splice(3, 0, '-')
      // 添加第二个横杠
      numberList.splice(8, 0, '-')
      // 合并为字符串
      return numberList.join('')
    },
    carNumValidator(val, value, callback) {
      const xreg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/
      const creg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/
      // const regExp = new RegExp(reg);
      if (this.isMobile) {
        if (!val) {
          return false
        } else if (val.length === 7 && !xreg.test(val)) {
          // 新能源车
          // callback(new Error('车牌号格式错误'))
          // callback();
          return true
        } else if (val.length === 8 && !creg.test(val)) {
          // 普通汽车
          // callback(new Error('车牌号格式错误'))
          // callback();
          return true
        } else if (![7, 8].includes(val.length)) {
          // callback(new Error('车牌号格式错误'));
          return false
        } else {
          callback()
          return true
        }
      } else {
        if (!value) {
          callback(new Error('请输入车牌号'))
        } else if (value.length === 7 && !xreg.test(value)) {
          // 新能源车
          // callback(new Error('车牌号格式错误'))
          callback()
        } else if (value.length === 8 && !creg.test(value)) {
          // 普通汽车
          // callback(new Error('车牌号格式错误'))
          callback()
        } else if (![7, 8].includes(value.length)) {
          callback(new Error('车牌号格式错误'))
        } else {
          callback()
        }
      }
    },
    getPlaceholder(unit) {
      if (unit === '元') {
        return '￥0.00'
      } else if (unit === '小时') {
        return '0'
      } else if (unit === '公里') {
        return '0.00'
      }
    },
    beforeInit(formData, passengerData, feeResData) {
      return new Promise((resolve) => {
        let result = {}
        // 合并用车列表数据
        if (passengerData) {
          Object.assign(result, {
            vdAfUseCarPersonList: passengerData[this.bizId].map((item) => ({
              ...item,
              currentKey: uuid(4, 8),
            })),
          })
        } else {
          Object.assign(result, { vdAfUseCarPersonList: [] })
        }
        // 合并费用填报数据
        if (feeResData) {
          // todo 后续可能支持添加多辆车辆，目前先选择第一项
          // 如果部分数据为null，需要手动补0
          const feeData = feeResData[this.bizId][0]
          const { remark1, ...feeRest } = feeData
          Object.keys(feeRest).forEach((key) => {
            if (!feeRest[key]) {
              // 支付方式，附件，燃油标号为空时，不需要补0
              const nonZeroKeys = [
                'attachment1',
                'attachment2',
                'attachment3',
                'cost4Field1',
                'cost1Type',
                'cost2Fj',
                'cost2Type',
                'cost3Fj',
                'cost3Type',
                'cost4Type',
                'cost4Fj',
                'cost5Fj',
                'cost5Type',
                'cost6Fj',
                'cost6Type',
                'cost7Fj',
                'cost7Type',
                'cost8Fj',
                'cost8Type',
                'cost9Fj',
                'cost9Type',
              ]
              if (nonZeroKeys.findIndex((item) => item === key) > -1) {
                feeRest[key] = ''
              } else {
                feeRest[key] = 0
              }
            }
          })
          feeRest.remark3 = remark1
          // 修改费用信息需要带上id
          Object.assign(result, feeRest, { id: formData.id })
        } else {
          // 将预计时间自动带入
          if (formData) {
            const timeObj = {
              startTime2: formData.startTime,
              endTime2: formData.endTime,
            }
            Object.assign(result, this.feeForm, timeObj)
          }
        }
        // 合并车辆/司机数据
        if (this.isXcDriverCarModify && this.vdDriverInfo && this.vdCarInfo) {
          Object.assign(result, this.vdCarInfo, this.vdDriverInfo)
        }
        // 合并申请表单数据
        result = { ...formData, ...result }
        // 返回处理的表单数据
        resolve(this.beforeInitFormData(result))
      })
    },
    beforeInitFormData(data) {
      return data
    },
    initFormData() {},
    // 操作前的校验
    validator() {
      if (this.isModify && !this.modifycomment) {
        this.isMobile && Toast('请填写变更原因')
        this.isPC && this.$message.warning('请填写变更原因')
        return false
      }
      return true
    },
    task100UserValidator(rule, value, callback) {
      const item = this.approveList1.find((item) => item.userName === value)
      if (item) {
        callback()
      } else {
        if (this.type !== 'view') {
          this.formData.task100UserName = ''
          this.formData.task100FullName = ''
          callback(new Error('该审批领导不存在'))
        }
      }
    },
    task200UserValidator(rule, value, callback) {
      const item = this.approveList2.find((item) => item.userName === value)
      if (item) {
        callback()
      } else {
        if (this.type !== 'view') {
          this.formData.task200UserName = ''
          this.formData.task200FullName = ''
          callback(new Error('该车辆调度员不存在'))
        }
      }
    },
    // 判断是否包含家庭相关信息
    containsFamilyInfo(input) {
      const familyRegex = /(小区|园|幢|栋|楼|家|房间号|车库|物业|楼下|地下车库|华东园)/
      return familyRegex.test(input)
    },
    checkSensitive(input) {
      // 判断路由，填写表单时才调接口
      let path = this.$route.path
      if (path !== '/projectCar/projectPortal/application') {
        return new Promise((resolve, reject) => {
          resolve({
            is_private: false,
            reason: '',
            private_part: '',
          })
        })
      }

      let param = {
        address: input,
      }
      return sensitiveDetection(param)
        .then((res) => {
          return res.data
        })
        .catch((error) => {
          // 捕获异常并返回初始化好的空对象
          return {
            is_private: false,
            reason: '',
            private_part: '',
          } // 返回初始化好的空对象
        })
    },
    // 判断途经点是否敏感
    isSensitiveViaPoint(index) {
      // 根据实际业务判断当前 viaPoint.address 是否含敏感词
      // 这里只是演示返回 false
      return false
    },
    // 添加途经点
    addViaPoint() {
      if (!this.formData.vdAfWaypointList) {
        this.formData.vdAfWaypointList = []
      }
      let list = this.formData.vdAfWaypointList
      let uniId = uuid(4, 8)
      list.push({ ...this.emptyViaAddr, uniId })
      this.$set(this.$data, 'vdAfWaypointList', list)
      this.wayPointsSensitive.push({
        notify: '',
        private: false,
      })
      this.generateFormRules()
    },
    // 删除途经点
    removeViaPoint(index) {
      this.formData.vdAfWaypointList.splice(index, 1)
      this.wayPointsSensitive.splice(index, 1)
      this.generateFormRules()
    },
    updateBracketPosition() {
      let totalHeight = this.addrTotalHeight
      const formItemEl = this.$refs.startFormItem.$el || this.$refs.startFormItem
      const formItemHeight = formItemEl ? formItemEl.offsetHeight : 0

      // 获取 fks-input 的根 DOM 元素
      const inputEl = this.$refs.startAddressInputRef.$el || this.$refs.startAddressInputRef
      const inputHeight = inputEl ? inputEl.offsetHeight : 0

      this.bracketHeight = totalHeight - formItemHeight + 4
      this.bracketTop = inputHeight / 2 + 2
    },
    reversAddr() {
      this.addressDontCheck = true
      const {
        startAddress,
        startAddressSmx,
        startAddressSmy,
        endAddress,
        endAddressSmx,
        endAddressSmy,
      } = this.formData
      const { endNotify, startNotify } = this.sensitiveContent
      this.sensitiveContent.startNotify = endNotify
      this.sensitiveContent.endNotify = startNotify
      let endShow = this.isSensitiveEndAddress
      this.isSensitiveEndAddress = this.isSensitiveStartAddress
      this.isSensitiveStartAddress = endShow

      // 交换出发地与目的地的地址、经纬度和 smy
      this.formData.startAddress = endAddress
      this.formData.startAddressSmx = endAddressSmx
      this.formData.startAddressSmy = endAddressSmy

      this.formData.endAddress = startAddress
      this.formData.endAddressSmx = startAddressSmx
      this.formData.endAddressSmy = startAddressSmy
      setTimeout(() => {
        this.addressDontCheck = false
      }, 100)
    },
    setupDraggable() {
      // 通过 ref 获取 transition-group 容器（真实 DOM 元素）

      const container = document.querySelector('.draggableContainer')
      if (!container) return
      Sortable.create(container, {
        handle: '.via-drag-handle', // 只有这个元素可以触发拖拽
        animation: 180,
        delay: 0,
        // 如果需要过滤某些元素可以启用 filter 选项
        // filter: ".draggable-filter",
        onStart: (evt) => {
          console.log('开始拖拽', evt)
        },
        onEnd: (evt) => {
          // evt.oldIndex 和 evt.newIndex 分别表示拖拽前后的位置
          let oldIndex = evt.oldIndex
          let newIndex = evt.newIndex
          // 取出拖拽项
          const movedItem = this.formData.vdAfWaypointList[oldIndex]
          // 删除原位置数据
          this.formData.vdAfWaypointList.splice(oldIndex, 1)
          // 插入到新位置
          this.formData.vdAfWaypointList.splice(newIndex, 0, movedItem)
          this.$nextTick(() => {
            // 通过访问 offsetWidth 强制浏览器重排
            if (container) {
              void container.offsetWidth
            }
            // 如果你有专门的布局刷新函数，可以在这里调用
            // this.doLayout();  // 自定义的布局刷新方法
          })
        },
      })
    },
    wayPointKeyCheck(index) {
      let data = this.formData.vdAfWaypointList[index]

      let refInstance = null
      let refsArray = this.$refs.viaPointHighlightRef
      if (Array.isArray(refsArray)) {
        refInstance = refsArray[index] // 根据 index 访问对应的组件
      }
      if (!refInstance) {
        return
      }

      if (refInstance) {
        refInstance.clearHighlight(data.address)
      }
      if (this.addressDontCheck) {
        return
      }
      this.wayPointsSensitive[index].notify = ''

      this.checkSensitive(data.address).then((res) => {
        this.wayPointsSensitive[index].private = res.is_private
        if (res.is_private) {
          this.wayPointsSensitive[index].notify = res.reason + '：' + res.private_part
        }

        let text = res.private_part
        if (refInstance) {
          refInstance.handleHighlight(text)
        }
      })
    },
    async searchWayPointList(id) {
      const wayPoints = await getWayPoints(id)
      let data = wayPoints.data
      if (data && data[id]) {
        let list = data[id]
        if (list.length > 0) {
          for (let index = 0; index < list.length; index++) {
            this.wayPointsSensitive.push({
              notify: '',
              private: false,
            })
            list[index].uniId = uuid(4, 8)
            this.wayPointKeyCheck(index)
          }
          this.formData.vdAfWaypointList = list
        }
      }

      this.generateFormRules()
    },

    saveDrafts() {
      return saveDrafts(this)
    },
    loadData() {
      return loadData(this)
    },
    clickFormRow() {
      return clickFormRow(this)
    },
    // 生成表单校验规则
    generateFormRules() {
      const env = this.isMobile ? 'mobile' : 'pc'
      const { getFormRules, generateDynamicFieldRules } = getRulesModule(env)

      let rules = getFormRules(this.currentFieldConfig, this)
      rules = generateDynamicFieldRules(rules, this.formData, this, this.currentFieldConfig)

      this.formRules = rules
    },
    computeFieldValue(prop) {
      if (prop === 'km3') {
        const km1 = Number(this.formData.km1 || 0)
        const km2 = Number(this.formData.km2 || 0)
        const diff = km2 - km1
        return diff >= 0 ? diff : ''
      }
      return ''
    },
    setReturnNodeListByKeys(taskKeys = []) {
      const { formData } = this

      const nodeDefs = [
        { value: 'UserTask_0', label: '用车申请', nameKey: 'task0FullName' },
        { value: 'UserTask_100', label: '领导审批', nameKey: 'task100FullName' },
        { value: 'UserTask_200', label: '调度员派车', nameKey: 'task200FullName' },
        { value: 'UserTask_300', label: '司机确认', nameKey: 'task300FullName' },
        { value: 'UserTask_400', label: '行车日志填报', nameKey: 'task400FullName' },
        { value: 'UserTask_500', label: '费用审批', nameKey: 'task500FullName' },
      ]

      const nodes = nodeDefs
        .filter((n) => taskKeys.includes(n.value))
        .map((n) => ({
          value: n.value,
          label: n.label,
          userName: formData[n.nameKey] || '',
        }))

      this.$emit('setReturnNodeList', nodes)
    },
    computeBtnKey(context) {
      const { formData, isCrossNodeReturn, isAdd } = context
      if (!isAdd) return
      if (isCrossNodeReturn) {
        return formData.flagBb ? ButtonKeys.FLAG_BB_REJECT : ButtonKeys.HANDLE_REJECT
      } else {
        return formData.flagBb ? ButtonKeys.FLAG_BB : ButtonKeys.HANDLE
      }
    },
  },
  watch: {
    isCrossNodeReturn: {
      handler(newVal) {
        if (this.isAdd) {
          let btnKey = this.computeBtnKey(this)
          this.initFormFieldConfig(this.taskKey, btnKey)
        }
      },
    },
    async isStartAndEndPointSelected(newVal) {
      const startPoint = { x: this.formData.startAddressSmx, y: this.formData.startAddressSmy }
      const endPoint = { x: this.formData.endAddressSmx, y: this.formData.endAddressSmy }
      const { timeStr, distanceStr } = await this.getTripInfo(startPoint, endPoint)
      this.timeStr = timeStr
      this.distanceStr = distanceStr
    },
    'vdProjectInfo.projectUseCarNature': {
      deep: true,
      immediate: true,
      handler(newVal) {
        if (this.isApply) {
          let natureList = this.vdProjectInfo.projectUseCarNature
          let defaultNature = natureList.split(',').map((item) => Number(item))[0]
          if (defaultNature) {
            const found = this.enums.UseCarNatureEnums.find((item) => item.key === defaultNature)
            this.$nextTick(() => {
              if (this.$refs.natureSelect) {
                this.$refs.natureSelect.confirm(found)
              } else {
                this.$set(this.formData, 'useCarNature', defaultNature)
              }
            })
          }
        }
      },
    },
    formData: {
      deep: true,
      handler: function (newVal) {
        this.saveDrafts()
      },
    },
    currentFieldConfig: {
      handler() {
        this.generateFormRules()
        if (this.isPC) {
          this.$nextTick(() => {
            const sections = Object.keys(this.$refs).filter(
              (key) => key.includes('section') && !!this.$refs[key]
            )
            this.tabs = formTabs.filter(
              (item) => sections.findIndex((section) => section === item.value) > -1
            )
            this.currentTab = this.tabs[0].value
          })
        }
      },
    },
  },
  async created() {
    const context = createInitContext(this)
    const chain = createInitChain(context) // chain 是连好了的链
    try {
      await chain.exec() // 从链头触发递归
      this.$emit('init-success') // 通知审批按钮组件可以显示了（bugfix：修复表单规则没初始化成功时，审批按钮可以直接点击提交的问题）
    } catch (e) {
      console.error('[ERROR] chain.handle failed:', e)
    }
  },
  mounted() {
    this.setupDraggable()

    // 给formData所有字段加上监听，如果有对应的handle则执行
    const fields = Object.keys(this.formData || {})
    fields.forEach((field) => {
      this.$watch(`formData.${field}`, (newVal, oldVal) => {
        applyFieldLinkage(field, newVal, this, oldVal)
      })
    })
  },
  beforeDestroy() {
    // 清除所有表单字段配置
    this.doCleanFieldInfo()
  },
}
