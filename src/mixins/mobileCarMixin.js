import {getApprovalList} from "@modules/CarApply/components/api";
import { Dialog, Toast } from 'fawkes-mobile-lib'
import platform from "@/mixins/platform";
import { changeTips } from '@utils/constants'
import { ButtonKeys } from '@store/modules/FormController/formFieldConfig'
import { deleteCarRecord } from '@modules/ProjectCar/ProjectPortal/CarRecord/api'

export default {
  mixins: [platform],
  methods: {
    parseAddress(address) {
      return window.AddressParse.parse(address);
    },
    showTripDetail(state){
      return state === 9 || state === 1
    },
    /*打开查看页面*/
    handleView(row, type) {
      const formData = {
        ...row
      }
      getApprovalList({bizId: row.id}).then((res) => {
        this.$store.commit('SET_CURRENT_ROW', {});
        if (res.data) {
          const obj = {
            ...row,
            formKey: 'vehicleDispatch',
            taskKey: res.data[res.data.length - 1].taskKey,
            taskName: res.data[res.data.length - 1].taskName,
            taskId: res.data[res.data.length - 1].taskId,
            bizId: res.data[res.data.length - 1].formBizId,
            processInstanceId: res.data[res.data.length - 1].processInstanceId,
            buttonKey: ButtonKeys.VIEW
          }
          if (type === 2 || type === 4) {
            this.$router.push({
              // name: 'formExecute',
              name: 'Application',
              params: {
                ...obj,
                type: 'execute',
                formName: '车辆调度'
              },
              query: {
                isApply: 'false',
                narrow: 'true',
                buttonKey: type === 4 ? row.buttonKey : null
              }
            }).then(() => {})
          } else {
            // type: 'execute',
            //   formKey: item.formKey,
            //   taskKey: item.taskKey,
            //   taskId: item.taskId,
            //   bizId: item.formBizId,
            //   processInstanceId: item.processInstanceId,
            //   formName: item.formName
            this.$router.push({
              // name: 'formView',
              name: 'Application',
              params: {
                ...obj,
                type: 'view'
              }
            }).then(() => {})
          }
        }
      })
    },
    /*点击按钮操作*/
    async handleOpt({buttonKey}, item) {
      const res = await getApprovalList({bizId: item.id})
      if (res.data) {
        const obj = {
          formKey: 'vehicleDispatch',
          taskKey: res.data[res.data.length - 1].taskKey,
          taskName: res.data[res.data.length - 1].taskName,
          taskId: res.data[res.data.length - 1].taskId,
          bizId: res.data[res.data.length - 1].formBizId,
          processInstanceId: res.data[res.data.length - 1].processInstanceId,
        }
        this.$store.commit('SET_CURRENT_ROW', {
          ...item,
          ...obj
        });
        if (buttonKey === 'VIEW') {
          const params = {
            ...obj,
            type: 'view'
          }
          if (this.isMobile) {
            await this.$router.push({
              name: 'Application',
              params
            })
          } else {
            this.formParams = params;
            this.$nextTick(() => {
              this.$refs.formCenterRef.drawerVisible = true;
            })
          }
        } else if (buttonKey === 'HANDLE') {
          const params = {
            ...obj,
            type: 'execute'
          }
          if (this.isMobile) {
            await this.$router.push({
              name: 'Application',
              params
            })
          } else {
            this.formParams = params;
            this.$nextTick(() => {
              this.$refs.formCenterRef.drawerVisible = true;
            })
          }

        } else if (buttonKey === 'REVOCATION') {
          this.revocation(item)
        } else if (
          buttonKey === 'XC_FORM_MODIFY' ||
          buttonKey === 'XC_DRIVER_CAR_MODIFY' ||
          buttonKey === 'XC_CAR_COMP_MODIFY' ||
          buttonKey === 'FORM_MODIFY_ADMIN'
        ) {
          if (this.isMobile) {
            await this.$router.push({
              name: 'Application',
              params: {
                ...obj,
                type: 'execute'
              },
              query: {
                buttonKey
              }
            })
          } else {
            this.formParams = {
              ...obj,
              type: 'execute',
              buttonKey
            }
            this.$nextTick(() => {
              this.$refs.formCenterRef.drawerVisible = true;
            })
          }
        } else if (buttonKey === 'XC_DELETE') {
          Dialog.confirm({
            title: '提示',
            message: '确认删除该行程记录？'
          }).then(() => {
            deleteCarRecord(item.id).then(res => {
              if (res.status) {
                const i = this.formList.findIndex(el => el.id === item.id)
                if (i > -1) {
                  this.formList.splice(i, 1)
                  this.isMobile && Toast('删除成功');
                }
              }
            })
          })
        }
      } else {
        this.isPC && this.$message.warning('表单数据异常')
        this.isMobile && Toast('表单数据异常');
      }

    },
    getLongitudeAndLatitude(item, name) {
      const smx = name + 'Smx'
      const smy = name + 'Smy'
      return {x: item[smx], y: item[smy]}
    },
    getCity(item, name = 'endAddress') {
      const {x, y} = this.getLongitudeAndLatitude(item, name)
      // 如果是用户手动输入的地址，城市地址为空
      if (x && y) {
        const address = this.parseAddress(item[name])[0];
        const {province, city} = address;
        // 如果province是直辖市，需要特殊处理
        const municipalities = ['北京', '上海', '天津', '重庆'];
        if (municipalities.includes(province)) {
          return city
        } else {
          return province + city
        }
      } else {
        return ''
      }
    },
    getAddress(item, name = 'endAddress') {
      const {x, y} = this.getLongitudeAndLatitude(item, name)
      if (x && y) {
        const address = this.parseAddress(item[name])[0];
        const {area, details} = address;
        return area + details;
      } else {
        // 如果是用户手动输入的地址，直接返回该地址
        return item[name];
      }
    },
  }
}
