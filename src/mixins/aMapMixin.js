import { loadScript } from '@utils/util'

export default {
  methods: {
    loadAMapUI() {
      return new Promise(resolve => {
        loadScript(
          'https://webapi.amap.com/ui/1.1/main.js?v=1.1.1',
          () => {
            console.log('加载amapui：', window.AMapUI)
            resolve()
          }
        )
      })
    },
    loadAMap() {
      return new Promise(resolve => {
        loadScript(
          'https://webapi.amap.com/maps?v=2.0&key=800428841b5be7ed2762b234a8e10544',
          () => {
            console.log('加载amap：', window.AMap)
            resolve()
          }
        )
      })
    },
    async initAMap() {
      if (!window.AMap) {
        console.log('没有amap')
        await this.loadAMap();
      }
      if (!window.AMapUI) {
        console.log('没有amapui')
        await this.loadAMapUI();
      }
    }
  }
}
