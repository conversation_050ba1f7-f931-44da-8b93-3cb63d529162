<template>
  <flow-table v-if="comments.length > 0" :data="comments" :bizId="bizId" :type="type" />
</template>
<script>
import FlowTable from "@components/FlowTable/index.vue";
import {getProcessHistory} from "@modules/FormCenter/api";
export default {
  name: 'CustomFlowTable',
  components: {FlowTable},
  props: ['bizId', 'type'],
  data() {
    return {
      comments: []
    }
  },
  created() {
    getProcessHistory({bizId: this.bizId}).then(
      (res) => {
        this.comments = res.data
      }
    )
  }
}
</script>
