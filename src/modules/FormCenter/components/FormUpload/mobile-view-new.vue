<template>
  <div>
    <fm-field
      :required="false"
      :readonly="disabled"
      label-width="100"
      label="上传附件"
      input-align="right"
      readonly
    >
      <template slot="suffix-icon">
        <fm-uploader
          :accept="accept"
          :readonly="disabled"
          :before-read="beforeRead"
          :after-read="afterRead"
        >
          <div class="flex col-center">
            <div class="count-text">{{fileList.length || 0}}个附件</div>
            <img
              v-if="!disabled"
              width="20px"
              height="20px"
              style="vertical-align: text-bottom; margin-left: 10px"
              :src="require('@/assets/img/attachment-plus.svg')"
            />
          </div>
        </fm-uploader>
      </template>
    </fm-field>
    <div v-if="fileList.length > 0">
      <div
        v-for="file in fileList"
        :key="file.fileToken"
        class="preview-file flex col-center row-between"
        style="margin-top: 12px"
        @click="handlePreview(file)"
      >
        <svg-icon
          :icon-class="getFileType(file.name)"
          class-name="file-icon"
          style="margin-right: 12px"
        />
        <div class="file-info flex flex-column row-between flex-grow-1">
          <span class="file-name">{{ truncateFileName(file.name) }}</span>
          <span class="file-size">{{ getFileSize(file.size) }}</span>
          <fm-progress
            v-if="file.progress > 0 && file.progress < 100"
            :percentage="file.progress"
          />
        </div>
        <fm-popover
          :visible.sync="file.showPopover"
          placement="bottom-end"
          trigger="click"
        >
          <div class="flex col-center" style="padding: 12px">
            <div class="flex flex-column col-center" style="margin-right: 10px;" @click="handlePreview(file)">
              <i class="fks-icon-view" style="font-size: 22px;margin-bottom: 8px;color: rgba(94, 160, 235, 1)" />
              <div class="popover-text">预览</div>
            </div>
<!--            todo 由于飞书移动端不支持文件下载，暂时隐藏-->
<!--            <div class="flex flex-column col-center" style="margin: 0 12px;" @click="handleDownload(file)">-->
<!--              <i class="fks-icon-download" style="font-size: 22px;margin-bottom: 8px;color: rgba(94, 160, 235, 1)" />-->
<!--              <div class="popover-text">下载</div>-->
<!--            </div>-->
            <div v-if="!disabled" class="flex flex-column col-center" @click="handleDelete(file)">
              <i class="fks-icon-delete" style="font-size: 22px;margin-bottom: 8px;color: rgba(255, 77, 79, 1)" />
              <div class="popover-text">删除</div>
            </div>
          </div>
          <template #reference>
            <svg-icon
              icon-class="file-more"
              class-name="more-icon"
              @click.native.stop="handleMore(file)"
            />
          </template>
        </fm-popover>
      </div>
    </div>
      <fks-image-viewer
        v-if="showImg"
        :url-list="picList"
        :initial-index="0"
        :on-close="handleImgClose"
      />
      <fm-popup
        :visible.sync="showFile" mode="center" close-on-click-modal
        style="width: 90%;max-height: 90vh;"
      >
        <div v-if="currentFile && showFile" style="height: calc(100vh - 330px)">
          <pre-view
            :file="currentFile"
            :key="currentFile.fileToken"
            @close="currentFile = null"
          />
        </div>
      </fm-popup>
  </div>
</template>

<script>
import { uuid } from '@/utils/util'
import { getFile } from '@/api/file'
import { deleteFile, downloadFile } from '@modules/FormCenter/api'
import request from '@utils/request'
import PreView from '@components/PreView/index.vue'
import FksImageViewer from '@components/PicturePreview/FksImageViewer.vue'
import { Toast, Dialog } from 'fawkes-mobile-lib'
import download from '@utils/downloadFile'
import formStyleMixin from '@components/InputAttachment/formStyleMixin'
import { getFileAsPdf } from '@components/PreView/api'

export default {
  name: 'form-upload-mobile-new',
  components: { FksImageViewer, PreView },
  mixins: [formStyleMixin],
  data() {
    return {
      fileList: [],
      g9s: uuid(16, 32),
      accessToken: this.$storage.get('access_token'),
      currentFile: null,
      showFile: false,
      showImg: false,
      picList: []
    }
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    accept: {
      type: String,
      default: '*/*',
    },
    value: {
      type: String,
      default: '',
    },
  },
  methods: {
    handleMore(file) {
      file.showPopover = !file.showPopover;
      this.$forceUpdate();
    },
    handleImgClose() {
      this.showImg = false;
    },
    async handlePreview(file) {
      file.showPopover = false;
      // 如果是图片文件
      if (this.getFileType(file.name) === 'image') {
        // 拼接图片路径
        this.picList = [`${process.env.VUE_APP_BASE_URL}/sys-storage/download_image?f8s=${file.fileToken}`]
        this.showImg = true;
      } else if (this.getFileType(file.name) === 'pdf') {

        getFileAsPdf(file.fileToken).then(async (res) => {

          const blob = res.data
          const arrayBuffer = await blob.arrayBuffer()
          let baseUrl = process.env.VUE_APP_BASE_URL
          baseUrl = baseUrl.replace('/api', '')
          pdfjsLib.GlobalWorkerOptions.workerSrc = baseUrl + '/pdfjs/build/pdf.worker.js'
          const loadingTask = pdfjsLib.getDocument({
            data: arrayBuffer,
            cMapUrl: baseUrl + '/pdfjs/webs/cmaps/',
            cMapPacked: true,
          })

          let imgFiles = []
          loadingTask.promise.then(async (pdf) => {
            const pageNum = pdf.numPages

            for (let i = 1; i <= pageNum; i++) {
              const page = await pdf.getPage(i)
              const viewport = page.getViewport({ scale: 2 }) // scale 决定清晰度

              const canvas = document.createElement('canvas')
              const context = canvas.getContext('2d')
              canvas.width = viewport.width
              canvas.height = viewport.height

              await page.render({ canvasContext: context, viewport }).promise

              const imgData = canvas.toDataURL('image/png')
              imgFiles.push(imgData) // 每一页 base64 图片 push 到数组
            }
          })
          this.picList = imgFiles;
          this.showImg = true;
        })
      } else if (this.getFileType(file.name) !== 'unknown') {
        // 处理pdf，word，excel
        this.currentFile = file
        this.showFile = true;
      }
    },
    handleDownload(file) {
      file.showPopover = false;
      this.$forceUpdate();
      downloadFile(file.fileToken).then(res => {
        download(file, res)
      })
    },
    handleDelete(file) {
      Dialog.confirm({
        // 组件除show外的属性
        title: '提示',
        message: '是否要删除该附件?',
      }).then(() => {
        file.showPopover = false;
        this.$forceUpdate();
        if (file.fileToken) {
          deleteFile({ f8s: [file.fileToken] }).then((res) => {
            if (res.status) {
              Toast.success('删除成功');
              this.fileList = this.fileList.filter((item) => item.fileToken !== file.fileToken);
            }
          })
        }
      })
    },
    beforeRead(file) {
      if (file.type.startsWith('image/') || file.type === 'application/pdf') {
        return true;
      }else {
        Toast('请上传.jpg,.jpeg,.png,.JPG,.JPEG,.pdf格式的文件!');
        return false;
      }
    },
    afterRead(files) {
      files.status = 'uploading'
      files.status = 'uploading'
      files.message = '上传中...'
      files.name = files.file.name
      files.progress = 0
      const index = this.fileList.length
      this.fileList.push(files)
      this.uploadFile({
        files,
        g9s: this.g9s,
        index,
      }).then((res) => {
        if (!res.status) {
          // 请求未成功  状态修改为失败
          files.status = 'failed'
          files.message = '上传失败'
          this.fileList.splice(index, 1)
          return false
        } else {
          // 请求成功  状态修改为成功
          files.message = '上传完成'
          files.status = 'done'
          // 把接口返回的数据放到数组里面
          files.progress = 100
          files.type = 'success'
          this.$set(this.fileList, index, { ...files, ...res.data })
        }
      })
    },
    uploadFile(params) {
      const { files, g9s, index } = params
      let formData = new FormData()
      formData.append('g9s', g9s)
      formData.append('file', files.file, files.file.name)
      const _this = this
      return request({
        url: '/sys-storage/upload',
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data',
          'Fawkes-Auth': this.accessToken,
        },
        onUploadProgress: (event) => {
          // 这里可以获取上传进度
          if (event.lengthComputable) {
            let progress = Math.ceil((event.loaded * 100) / event.total)
            if (progress <= 100) {
              files.progress = progress
              _this.$set(_this.fileList, index, files)
            }
          }
        },
        data: formData,
      })
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.g9s = newVal
          // 获取文件列表
          const fileArr = newVal.split(',')
          getFile({ g9s: fileArr }).then((res) => {
            if (res.status) {
              this.fileList = res.data.map((item) => {
                return {
                  fileName: item.fileName,
                  name: item.fileName,
                  size: item.size,
                  fileToken: item.fileToken,
                }
              })
            }
          })
        } else {
          // 初始化g9s
          this.$emit('input', this.g9s)
        }
      },
    },
  },
}
</script>

<style lang="less" scoped>
@import './exclude/mobile-view-new.css';
</style>
