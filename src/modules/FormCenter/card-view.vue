<template>
<div class="flex flex-column" style="overflow: hidden;">
  <component
    style="overflow-x: hidden"
    class="flex-grow-1 overflow-y-auto"
    :is="dynamicForm"
    ref="formTemp"
    :bizId="bizId"
    :taskKey="taskKey"
    :type="type"
    :currentButtonKey="currentButtonKey"
    :params="params"
    v-bind="$attrs"
    viewType="drawer"
    @setEntityName="setEntityName"
    @setFormName="setFormName"
    @setModelKey="setModelKey"
    @setPreSaveValidateProps="setPreSaveValidateProps"
    @setService="setService"
    @setSubmitText="setSubmitText"
    @setFormData="submitFormData"
    @setReturnNodeList="setReturnNodeList"
    @init-success="showApproveBtn = true"
    :isCrossNodeReturn="isCrossNodeReturn"
  />
  <approve-buttons
    v-if="this.type !== 'view' && showApproveBtn"
    :button-loading="buttonLoading"
    :taskKey="taskKey"
    :type="type"
    :bizId="bizId"
    :submit-text="submitText"
    :currentButtonKey="currentButtonKey"
    :disable-submit="projectClosed"
    :returnNodeList="returnNodeList"
    viewType="drawer"
    @onAction="onAction"
    @revocation="$emit('revocation')"
    @refresh="$emit('refresh', $event)"
    @modify="$emit('modify', $event)"
    @getButtonList="getButtonList"
  />
</div>
</template>

<script>
import mixins from './mixins';
import {UserTask_300} from "@utils/constants";
import ApproveButtons from "@modules/FormCenter/components/ApproveButtons/index.vue";
import FlowTable from "@components/FlowTable/index.vue";
import { mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
export default {
  name: "card-view",
  components: {FlowTable, ApproveButtons},
  mixins: [mixins],
  provide() {
    return {
      appendButtons: this.appendButtons
    }
  },
  data() {
    return {
      // 当前表单能返回的流程节点列表
      returnNodeList: [],
    }
  },
  computed: {
    ...mapState([StateTypes.PORTAL_STATUS_TABLE, StateTypes.IS_PROJECT_CLOSED]),
    projectClosed() {
      if (this.params.projectId) {
        return this[StateTypes.PORTAL_STATUS_TABLE][this.params.projectId] === 200
      } else {
        return this[StateTypes.IS_PROJECT_CLOSED]
      }
    },
    appendButtons() {
      return this.params.buttonsSmall || [];
    },
  },
  methods: {
    getButtonList(data) {
      this.formBtnList = data;
    },
    setReturnNodeList(list) {
      this.returnNodeList = list;
    },
    submitFormData(payload) {
      this.$emit('setFormData', payload)
    },
  }
}
</script>
