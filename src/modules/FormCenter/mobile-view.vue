<template>
  <div class="main-container" style="height: 100%">
    <!-- 顶部导航栏 -->
    <fm-nav-bar
      :border="false"
      :left-arrow="hasLeftArrow"
      left-text=""
      right-text=""
      @click-left="goBack"
    >
      <template slot="title">
        <!-- 标题区域：显示表单名称和提示图标 -->
        <div class="flex col-baseline">
          <span>{{ formName }}</span>
          <!-- 根据按钮权限显示提示图标 -->
          <i v-if="isNotifyButtonKey" class="m-l-16 fks-icon-info-outline" style="color: #999999;font-size: 16px" @click="handleTipClick" />
        </div>
      </template>
    </fm-nav-bar>

    <!-- 标签页导航：仅在非添加模式下显示 -->
    <fm-tabs v-if="showTabs" line-width="80px" @tab-click="tabClick" v-model="tab">
      <template v-for="(item, index) in visibleTabTitles">
        <fm-tab :key="index" :label="item" :name="item"></fm-tab>
      </template>
    </fm-tabs>

    <!-- 项目关闭提示 -->
    <fks-alert v-if="isClosed" title="项目已关闭，无法发起派车申请" type="error" style="width: unset;margin: 0 15px" />
    <!-- 添加/草稿模式：显示表单详情和操作按钮 -->
    <div v-if="isAddOrDraftMode" class="component task-detail-form">
      <!-- 动态表单组件 -->
      <dynamic-form-component
        ref="formTemp"
        :bizId="bizId"
        :taskKey="taskKey"
        :type="type"
        :currentButtonKey="currentButtonKey"
        v-bind="$attrs"
        @setEntityName="setEntityName"
        @setFormName="setFormName"
        @setModelKey="setModelKey"
        @setPreSaveValidateProps="setPreSaveValidateProps"
        @setService="setService"
        @setSubmitText="setSubmitText"
        @setReturnNodeList="setReturnNodeList"
        @init-success="handleInitSuccess"
        :isCrossNodeReturn="isCrossNodeReturn"
      />

      <!-- 审批按钮：仅在详情tab且显示按钮时显示 -->
      <approve-buttons
        v-show="isDetailTab && showApproveBtn"
        :button-loading="buttonLoading"
        :taskKey="taskKey"
        :type="type"
        :biz-id="bizId"
        :currentButtonKey="currentButtonKey"
        :submit-text.sync="submitText"
        :disable-submit="isClosed"
        :getComment="getComment"
        :getReturnNode="getReturnNode"
        @onAction="onAction"
        @getButtonList="getButtonList"
      />

      <!-- 流转时间线：仅在流转tab时显示 -->
      <div v-show="isFlowTab" class="content-container">
        <flow-timeline :bizId="bizId" :taskId="taskId"></flow-timeline>
      </div>
    </div>
    <!-- 任务待办模式：显示表单详情和审批操作 -->
    <div v-else :style="detailTabStyle">
      <!-- 表单详情容器：仅在详情tab时显示 -->
      <div
        v-show="isDetailTab"
        class="content-container x2"
        :style="{ 'margin-bottom': hasMultipleTabs ? '0px' : 0 }"
      >
        <!-- 动态表单组件 -->
        <dynamic-form-component
          ref="formTemp"
          :bizId="bizId"
          :taskKey="taskKey"
          :type="type"
          :currentButtonKey="currentButtonKey"
          v-bind="$attrs"
          @setEntityName="setEntityName"
          @setFormName="setFormName"
          @setModelKey="setModelKey"
          @setPreSaveValidateProps="setPreSaveValidateProps"
          @setService="setService"
          @setSubmitText="setSubmitText"
          @setReturnNodeList="setReturnNodeList"
          @init-success="handleInitSuccess"
          :isCrossNodeReturn="isCrossNodeReturn"
        />
      </div>

      <!-- 审批按钮：仅在详情tab且执行模式且显示按钮时显示 -->
      <approve-buttons
        v-if="shouldShowExecuteButtons"
        :button-loading="buttonLoading"
        :taskKey="taskKey"
        :type="type"
        :biz-id="bizId"
        :current-button-key="currentButtonKey"
        :submit-text.sync="submitText"
        :disable-submit="isClosed"
        :getComment="getComment"
        :getReturnNode="getReturnNode"
        :currentButtonKey="currentButtonKey"
        @onAction="onAction"
        @getButtonList="getButtonList"
      />
    </div>
    <div v-show="tab === 1" class="content-container">
      <flow-timeline ref="timelineRef" :bizId="bizId" :taskId="taskId"></flow-timeline>
    </div>
    <div
      v-show="
        tab === 2 || (tab === 0 && (tabTitles.length === 2 ? !!$route.query.buttonKey : true))
      "
      class="content-container"
    >
      <flow-approval
        ref="flowApproval"
        :afterSubmit="afterSubmit"
        :aftereReject="afterReject"
        :beforeSubmit="beforeSubmit"
        :checkRules="checkRules"
        :detailParamList="detailParamList"
        :entityName="entityName"
        :formData="formData"
        :from.sync="from"
        :formName.sync="formName"
        :returnNodeList="returnNodeList"
        :getEntityParam="getEntityParam"
        :is-default="isDefault"
        :modelKey="flowData.modelKey"
        :submit-text.sync="submitText"
        :submitURL="service.submit"
        :taskKey="taskKey"
        :biz-id="bizId"
        :show-btn="false"
        :is-show="
          !!$route.query.buttonKey
            ? ![
                'XC_DRIVER_CAR_MODIFY',
                'XC_FORM_MODIFY',
                'XC_CAR_COMP_MODIFY',
                'FORM_MODIFY_ADMIN',
              ].includes($route.query.buttonKey)
            : tab === 2
        "
      >
      </flow-approval>
      <approve-buttons
        v-if="showApproveBtn"
        :button-loading="buttonLoading"
        :taskKey="taskKey"
        :biz-id="bizId"
        :submit-text.sync="submitText"
        :disable-submit="isClosed"
        :type="type"
        :currentButtonKey="currentButtonKey"
        :getComment="getComment"
        :getReturnNode="getReturnNode"
        @onAction="onAction"
        @getButtonList="getButtonList"
      />
    </div>
    <div v-show="tab === 3" class="content-container">
      <flow-entrust :formData="formData"></flow-entrust>
    </div>
    <notification-confirm-mobile ref="notificationRef" />
  </div>
</template>
<script>
import mixins from './mixins'
import * as StateTypes from '@store/State/stateTypes'
import { getApprovalList } from './api'
import flowTimeline from './components/FlowTimeline/index.vue'
import flowEntrust from './components/FlowEntrust/index.vue'
import flowApproval from './components/FlowApproval/index.vue'
import CarApply from '@modules/FormCenter/CarApply/index.vue'
import ApproveButtons from '@modules/FormCenter/components/ApproveButtons/index.vue'
import NotificationConfirmMobile from '@components/NotificationConfirm/mobile-view.vue'
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'
import { changeTips } from '@utils/constants'
import { mapState } from 'vuex'
export default {
  name: 'FormCenterMobileView',
  components: {
    NotificationConfirmMobile,
    CarApply,
    flowApproval,
    flowEntrust,
    flowTimeline,
    ApproveButtons,
  },
  mixins: [mixins, isConfirmXCMixin],
  data() {
    return {
      tabTitles: [],
      tab: 0,
      from: {},
      formData: {},
      isDefault: true, // true 使用原来的请求参数格式 false不是使用
      detailParamList: [], // 需提交的明细表数据
      submitText: '提交',
      buttonList: [],
      returnNodeList: [],
      isClosed: false
    }
  },
  computed: {
    ...mapState([StateTypes.PORTAL_STATUS_TABLE, StateTypes.IS_PROJECT_CLOSED]),
    getHeight() {
      if (this.tab === 0 && this.type === 'edit' && this.buttonList.length > 0) {
        return 'calc(100% - 149px)'
      } else {
        if (this.type === 'view') {
          return 'calc(100% - 88px)'
        } else {
          return 'calc(100% - 140px)'
        }
      }
    },
    hasLeftArrow() {
      return Boolean(this[StateTypes.APPLY_RESOURCE] !== 1)
    },
  },
  methods: {
    handleInitSuccess() {
      this.showApproveBtn = true;
      this.$nextTick(() => {
        const projectId = this.$refs.formTemp.formData.projectId;
        if (projectId) {
          this.isClosed = this[StateTypes.PORTAL_STATUS_TABLE][projectId] === 200
        } else {
          this.isClosed = this[StateTypes.IS_PROJECT_CLOSED]
        }
      })
    },
    setReturnNodeList(list) {
      this.returnNodeList = list;
      // 获得lastItem，写入
      const lastItem = Array.isArray(list) && list.length > 0 ? list[list.length - 1] : null;
      this.$refs.flowApproval.setDefaultReturnNode(lastItem);
    },
    handleTipClick() {
      const content = changeTips[this.currentButtonKey];
      this.$dialog.alert({message: content});
    },
    getComment() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.comment : ''
    },
    getReturnNode() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.returnedNode : ''
    },
    getButtonList(data) {
      this.buttonList = data
      this.formBtnList = data;
    },
    async init() {
      if (this.type !== 'execute') {
        this.tabTitles = ['详情', '流转']
        return false
      }
      let params = {
        taskId: this.taskId,
        bizId: this.bizId,
      }
      if (Object.keys(params).length === 0) {
        return false
      }
      const res = await getApprovalList(params)
      this.approvalList = res.data
      if (res.data?.at(-1).taskKey === 'UserTask_0') {
        this.tabTitles = ['详情', '流转']
        // 判断详情
        this.taskKey = res.data[res.data.length - 1].taskKey
      } else {
        this.tabTitles = ['详情', '流转', '审批', '委托']
      }
      // 判断退回
      if (res.data.length > 1 && res.data?.at(-2).approveState === 'reject') {
        if (res.data?.at(-1).taskKey === 'UserTask_0') {
          // this.type = 'edit'
          // this.$route.params.type = 'edit'
        }
      }
      //判断暂存
      if (
        res.data.length === 1 &&
        res.data?.at(-1).taskKey === 'UserTask_0' &&
        res.data?.at(-1).approveState === 'stage'
      ) {
        // this.type = 'edit'
        // this.$route.params.type = 'edit'
      }
    },
    goBack() {
      this.type === 'add'
        ? this.$router.push({ path: '/projectCar/projectPortal/carRecord' })
        : this.$router.go(-1)
      // this.$router.go(-1);
    },
    tabClick(val) {
      this.tab = val
    },
    getEntityParam(item) {
      return this.$refs.formTemp?.getEntityParam({
        ...item,
        targetKey: 'submit',
        customId: this.customId,
      })
    },
    // 提交流程后的操作
    afterSubmit() {},
    // 表单退回之后的操作
    afterReject() {},
    // 提交流程前钩子函数
    beforeSubmit() {
      this.$refs.formTemp?.beforeSubmit()
    },
    // 非空校验方法
    checkRules() {
      // 业务表单提供校验方法
      if (this.$refs.formTemp.validator) {
        return this.$refs.formTemp.validator()
      } else {
        return true
      }
    },
    setSubmitText(submitText) {
      this.submitText = submitText
    },
  },
  watch: {
    currentButtonKey: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.isNotifyButtonKey) {
          this.getNotification(newVal).then(res => {
            if (res === false) {
              this.$refs.notificationRef.open({buttonKey: newVal, checked: res});
            }
          })
        }
      }
    }
  },
  mounted() {
    this.init()
  }
}
</script>

<style scoped lang="less">
@import './mobile-view';
</style>
