<template>
  <div class="main-container" style="height: 100%">
    <!-- 顶部导航栏 -->
    <fm-nav-bar
      :border="false"
      :left-arrow="hasLeftArrow"
      left-text=""
      right-text=""
      @click-left="goBack"
    >
      <template slot="title">
        <!-- 标题区域：显示表单名称和提示图标 -->
        <div class="flex col-baseline">
          <span>{{ formName }}</span>
          <!-- 根据按钮权限显示提示图标 -->
          <i v-if="isNotifyButtonKey" class="m-l-16 fks-icon-info-outline" style="color: #999999;font-size: 16px" @click="handleTipClick" />
        </div>
      </template>
    </fm-nav-bar>

    <!-- 标签页导航：仅在非添加模式下显示 -->
    <fm-tabs v-if="showTabs" line-width="80px" @tab-click="tabClick" v-model="tab">
      <template v-for="(item, index) in visibleTabTitles">
        <fm-tab :key="index" :label="item" :name="item"></fm-tab>
      </template>
    </fm-tabs>

    <!-- 项目关闭提示 -->
    <fks-alert v-if="isClosed" title="项目已关闭，无法发起派车申请" type="error" style="width: unset;margin: 0 15px" />

    <!-- 主内容区域 -->
    <div :class="mainContentClass" :style="mainContentStyle">
      <!-- 动态表单组件：统一的表单组件 -->
      <div v-show="shouldShowForm" :class="formContainerClass" :style="formContainerStyle">
        <component
          :is="dynamicForm"
          ref="formTemp"
          v-bind="formComponentProps"
          v-on="formComponentEvents"
        />
      </div>

      <!-- 流转时间线：仅在流转tab时显示 -->
      <div v-show="isFlowTab" class="content-container">
        <flow-timeline ref="timelineRef" :bizId="bizId" :taskId="taskId"></flow-timeline>
      </div>

      <!-- 审批流程：在审批tab或特定条件下显示 -->
      <div v-show="shouldShowApprovalFlow" class="content-container">
        <flow-approval
          ref="flowApproval"
          v-bind="flowApprovalProps"
        />
      </div>

      <!-- 消息通知：仅在消息通知tab时显示 -->
      <div v-show="isNotificationTab" class="content-container">
        <!-- TODO: 添加消息通知组件 -->
        <div class="notification-placeholder">
          <p>消息通知功能开发中...</p>
        </div>
      </div>
    </div>

    <!-- 统一的审批按钮组件 -->
    <approve-buttons
      v-if="shouldShowApproveButtons"
      v-bind="approveButtonsProps"
      v-on="approveButtonsEvents"
    />
    <notification-confirm-mobile ref="notificationRef" />
  </div>
</template>
<script>
import mixins from './mixins'
import * as StateTypes from '@store/State/stateTypes'
import { getApprovalList } from './api'
import flowTimeline from './components/FlowTimeline/index.vue'
import flowApproval from './components/FlowApproval/index.vue'
import CarApply from '@modules/FormCenter/CarApply/index.vue'
import ApproveButtons from '@modules/FormCenter/components/ApproveButtons/index.vue'
import NotificationConfirmMobile from '@components/NotificationConfirm/mobile-view.vue'
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'
import { changeTips } from '@utils/constants'
import { mapState } from 'vuex'

// Constants
const MODIFY_BUTTON_KEYS = ['XC_DRIVER_CAR_MODIFY', 'XC_FORM_MODIFY', 'XC_CAR_COMP_MODIFY']
const EXCLUDED_BUTTON_KEYS = [...MODIFY_BUTTON_KEYS, 'FORM_MODIFY_ADMIN']
const TAB_NAMES = {
  DETAIL: '详情',
  FLOW: '流转',
  APPROVAL: '审批',
  NOTIFICATION: '消息通知'
}
export default {
  name: 'FormCenterMobileView',
  components: {
    NotificationConfirmMobile,
    CarApply,
    flowApproval,
    flowTimeline,
    ApproveButtons,
  },
  mixins: [mixins, isConfirmXCMixin],
  data() {
    return {
      tabTitles: [], // 所有可用的tab标题列表
      tab: TAB_NAMES.DETAIL, // 当前激活的tab，默认为详情tab
      from: {}, // 来源信息
      formData: {}, // 表单数据
      isDefault: true, // true 使用原来的请求参数格式 false不是使用
      detailParamList: [], // 需提交的明细表数据
      submitText: '提交', // 提交按钮文本
      buttonList: [], // 按钮列表
      returnNodeList: [], // 可退回节点列表
      isClosed: false // 项目是否已关闭
    }
  },
  computed: {
    ...mapState([StateTypes.PORTAL_STATUS_TABLE, StateTypes.IS_PROJECT_CLOSED]),

    /**
     * 计算详情tab的样式
     * 根据当前tab、类型和按钮列表动态计算高度
     */
    detailTabStyle() {
      return this.isDetailTab
        ? [{ height: this.getHeight }, { ['overflow-y']: 'auto', ['overflow-x']: 'hidden' }]
        : {}
    },

    /**
     * 计算容器高度
     * 根据不同的模式和状态计算合适的高度
     */
    getHeight() {
      if (this.isDetailTab && this.type === 'edit' && this.buttonList.length > 0) {
        return 'calc(100% - 149px)'
      } else {
        if (this.type === 'view') {
          return 'calc(100% - 88px)'
        } else {
          return 'calc(100% - 140px)'
        }
      }
    },

    /**
     * 是否显示返回箭头
     * 根据应用资源状态判断
     */
    hasLeftArrow() {
      return Boolean(this[StateTypes.APPLY_RESOURCE] !== 1)
    },

    /**
     * 是否为添加或草稿模式
     */
    isAddOrDraftMode() {
      return this.type === 'add' || this.type === 'draft'
    },

    /**
     * 是否显示tabs
     * 仅在非添加模式下显示
     */
    showTabs() {
      return this.type !== 'add'
    },

    /**
     * 过滤后的可见tab标题列表
     * 根据按钮权限过滤不需要显示的tab
     */
    visibleTabTitles() {
      return this.tabTitles.filter(item => {
        // 如果是修改相关按钮，不显示审批tab
        if (MODIFY_BUTTON_KEYS.includes(this.$route.query.buttonKey) && item === TAB_NAMES.APPROVAL) {
          return false
        }

        // 消息通知tab的显示逻辑
        if (item === TAB_NAMES.NOTIFICATION) {
          return this.shouldShowNotificationTab
        }

        return true
      })
    },

    /**
     * 是否有多个tabs
     */
    hasMultipleTabs() {
      return this.tabTitles.length > 2
    },

    /**
     * 当前是否为详情tab
     */
    isDetailTab() {
      return this.tab === TAB_NAMES.DETAIL
    },

    /**
     * 当前是否为流转tab
     */
    isFlowTab() {
      return this.tab === TAB_NAMES.FLOW
    },

    /**
     * 当前是否为审批tab
     */
    isApprovalTab() {
      return this.tab === TAB_NAMES.APPROVAL
    },

    /**
     * 当前是否为消息通知tab
     */
    isNotificationTab() {
      return this.tab === TAB_NAMES.NOTIFICATION
    },

    /**
     * 是否应该显示消息通知tab
     * 显示条件：
     * 1. 当前用户是车辆调度员
     * 2. 流程必须在调度员派车通过后（taskKey > UserTask_200）
     */
    shouldShowNotificationTab() {
      // TODO: 需要实现判断当前用户是否为车辆调度员的逻辑
      const isVehicleDispatcher = this.isCurrentUserVehicleDispatcher()

      // 判断流程是否在调度员派车之后
      const isAfterDispatcherApproval = this.isTaskAfterDispatcherApproval()

      return isVehicleDispatcher && isAfterDispatcherApproval
    },

    /**
     * TODO: 判断当前用户是否为车辆调度员
     * 需要根据实际的角色判断逻辑来实现
     * @returns {boolean} 是否为车辆调度员
     */
    isCurrentUserVehicleDispatcher() {
      // TODO: 实现车辆调度员角色判断逻辑
      // 可能的实现方式：
      // 1. 检查用户角色列表中是否包含车辆调度员角色
      // 2. 检查用户权限中是否有调度相关权限
      // 3. 检查当前流程中用户是否为指定的调度员

      // 临时返回 false，需要根据实际业务逻辑实现
      console.info('🚀🚀', 'this.comments -->', this.comments, `<-- mobile-view.vue/isCurrentUserVehicleDispatcher`)
      console.warn('TODO: 需要实现车辆调度员角色判断逻辑')
      return false
    },

    /**
     * 判断当前流程是否在调度员派车之后
     * @returns {boolean} 是否在调度员派车之后
     */
    isTaskAfterDispatcherApproval() {
      if (!this.taskKey) return false

      // 定义任务节点的顺序
      const taskOrder = {
        'UserTask_0': 0,     // 用车申请
        'UserTask_100': 100, // 领导审批
        'UserTask_200': 200, // 调度员派车
        'UserTask_300': 300, // 司机确认
        'UserTask_400': 400, // 行车日志填报
        'UserTask_500': 500  // 费用审批
      }

      const currentTaskOrder = taskOrder[this.taskKey] || 0
      const dispatcherTaskOrder = taskOrder['UserTask_200'] || 200

      return currentTaskOrder > dispatcherTaskOrder
    },

    /**
     * 是否应该显示执行模式的按钮
     */
    shouldShowExecuteButtons() {
      return this.isDetailTab && this.type === 'execute' && this.showApproveBtn
    },

    /**
     * 是否应该显示审批流程
     */
    shouldShowApprovalFlow() {
      return this.isApprovalTab || (this.isDetailTab && this.shouldShowApprovalInDetail)
    },

    /**
     * 在详情tab中是否应该显示审批组件
     */
    shouldShowApprovalInDetail() {
      return this.hasMultipleTabs ? !!this.$route.query.buttonKey : true
    },

    /**
     * 审批组件是否应该显示
     */
    shouldShowApprovalComponent() {
      if (this.$route.query.buttonKey) {
        return !EXCLUDED_BUTTON_KEYS.includes(this.$route.query.buttonKey)
      }
      return this.isApprovalTab
    },

    /**
     * 主内容区域的CSS类
     */
    mainContentClass() {
      return this.isAddOrDraftMode ? 'component task-detail-form' : ''
    },

    /**
     * 主内容区域的样式
     */
    mainContentStyle() {
      return this.isAddOrDraftMode ? {} : this.detailTabStyle
    },

    /**
     * 是否应该显示表单
     */
    shouldShowForm() {
      return this.isDetailTab || this.isAddOrDraftMode
    },

    /**
     * 表单容器的CSS类
     */
    formContainerClass() {
      if (this.isAddOrDraftMode) {
        return ''
      }
      return 'content-container x2'
    },

    /**
     * 表单容器的样式
     */
    formContainerStyle() {
      if (this.isAddOrDraftMode) {
        return {}
      }
      return { 'margin-bottom': this.hasMultipleTabs ? '0px' : 0 }
    },

    /**
     * 是否应该显示审批按钮
     */
    shouldShowApproveButtons() {
      if (this.isAddOrDraftMode) {
        return this.isDetailTab && this.showApproveBtn
      }
      if (this.shouldShowExecuteButtons) {
        return true
      }
      if (this.shouldShowApprovalFlow && this.showApproveBtn) {
        return true
      }
      return false
    },

    /**
     * 表单组件的属性
     */
    formComponentProps() {
      return {
        bizId: this.bizId,
        taskKey: this.taskKey,
        type: this.type,
        currentButtonKey: this.currentButtonKey,
        isCrossNodeReturn: this.isCrossNodeReturn,
        ...this.$attrs
      }
    },

    /**
     * 表单组件的事件
     */
    formComponentEvents() {
      return {
        setEntityName: this.setEntityName,
        setFormName: this.setFormName,
        setModelKey: this.setModelKey,
        setPreSaveValidateProps: this.setPreSaveValidateProps,
        setService: this.setService,
        setSubmitText: this.setSubmitText,
        setReturnNodeList: this.setReturnNodeList,
        'init-success': this.handleInitSuccess
      }
    },

    /**
     * 审批流程组件的属性
     */
    flowApprovalProps() {
      return {
        afterSubmit: this.afterSubmit,
        aftereReject: this.afterReject,
        beforeSubmit: this.beforeSubmit,
        checkRules: this.checkRules,
        detailParamList: this.detailParamList,
        entityName: this.entityName,
        formData: this.formData,
        from: this.from,
        formName: this.formName,
        returnNodeList: this.returnNodeList,
        getEntityParam: this.getEntityParam,
        'is-default': this.isDefault,
        modelKey: this.flowData.modelKey,
        'submit-text': this.submitText,
        submitURL: this.service.submit,
        taskKey: this.taskKey,
        'biz-id': this.bizId,
        'show-btn': false,
        'is-show': this.shouldShowApprovalComponent
      }
    },

    /**
     * 审批按钮组件的属性
     */
    approveButtonsProps() {
      const baseProps = {
        'button-loading': this.buttonLoading,
        taskKey: this.taskKey,
        type: this.type,
        'biz-id': this.bizId,
        currentButtonKey: this.currentButtonKey,
        'submit-text': this.submitText,
        'disable-submit': this.isClosed,
        getComment: this.getComment,
        getReturnNode: this.getReturnNode
      }

      // 为执行模式添加额外属性
      if (this.shouldShowExecuteButtons) {
        return {
          ...baseProps,
          'current-button-key': this.currentButtonKey
        }
      }

      return baseProps
    },

    /**
     * 审批按钮组件的事件
     */
    approveButtonsEvents() {
      return {
        onAction: this.onAction,
        getButtonList: this.getButtonList
      }
    },
  },
  methods: {
    /**
     * 表单初始化成功回调
     * 设置审批按钮显示状态并检查项目关闭状态
     */
    handleInitSuccess() {
      this.showApproveBtn = true;
      this.$nextTick(() => {
        const projectId = this.$refs.formTemp.formData.projectId;
        if (projectId) {
          this.isClosed = this[StateTypes.PORTAL_STATUS_TABLE][projectId] === 200
        } else {
          this.isClosed = this[StateTypes.IS_PROJECT_CLOSED]
        }
      })
    },

    /**
     * 设置可退回节点列表
     * @param {Array} list - 可退回的节点列表
     */
    setReturnNodeList(list) {
      this.returnNodeList = list;
      // 获得最后一个节点，设置为默认退回节点
      const lastItem = Array.isArray(list) && list.length > 0 ? list[list.length - 1] : null;
      this.$refs.flowApproval.setDefaultReturnNode(lastItem);
    },

    /**
     * 处理提示图标点击事件
     * 显示对应按钮的提示信息
     */
    handleTipClick() {
      const content = changeTips[this.currentButtonKey];
      this.$dialog.alert({message: content});
    },

    /**
     * 获取审批意见
     * @returns {string} 审批意见内容
     */
    getComment() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.comment : ''
    },

    /**
     * 获取退回节点
     * @returns {string} 退回节点信息
     */
    getReturnNode() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.returnedNode : ''
    },

    /**
     * 获取按钮列表回调
     * @param {Array} data - 按钮列表数据
     */
    getButtonList(data) {
      this.buttonList = data
      this.formBtnList = data;
    },
    /**
     * 初始化组件
     * 根据类型设置tab标题并获取审批列表
     */
    async init() {
      // 非执行模式只显示详情和流转tab
      if (this.type !== 'execute') {
        this.tabTitles = [TAB_NAMES.DETAIL, TAB_NAMES.FLOW]
        return false
      }

      // 构建请求参数
      let params = {
        taskId: this.taskId,
        bizId: this.bizId,
      }

      if (Object.keys(params).length === 0) {
        return false
      }

      // 获取审批列表数据
      const res = await getApprovalList(params)
      this.approvalList = res.data

      // 根据最后一个任务节点判断显示的tab
      if (res.data?.at(-1).taskKey === 'UserTask_0') {
        this.tabTitles = [TAB_NAMES.DETAIL, TAB_NAMES.FLOW]
        // 设置当前任务键
        this.taskKey = res.data[res.data.length - 1].taskKey
      } else {
        this.tabTitles = [TAB_NAMES.DETAIL, TAB_NAMES.FLOW, TAB_NAMES.APPROVAL, TAB_NAMES.NOTIFICATION]
      }

      // 判断退回状态
      if (res.data.length > 1 && res.data?.at(-2).approveState === 'reject') {
        if (res.data?.at(-1).taskKey === 'UserTask_0') {
          // 可以在此处理退回逻辑
          // this.type = 'edit'
          // this.$route.params.type = 'edit'
        }
      }

      // 判断暂存状态
      if (
        res.data.length === 1 &&
        res.data?.at(-1).taskKey === 'UserTask_0' &&
        res.data?.at(-1).approveState === 'stage'
      ) {
        // 可以在此处理暂存逻辑
        // this.type = 'edit'
        // this.$route.params.type = 'edit'
      }
    },

    /**
     * 返回上一页
     * 根据类型决定返回路径
     */
    goBack() {
      this.type === 'add'
        ? this.$router.push({ path: '/projectCar/projectPortal/carRecord' })
        : this.$router.go(-1)
    },

    /**
     * tab点击事件处理
     * 现在接收tab名称而非索引
     * @param {string} tabName - 点击的tab名称
     */
    tabClick(tabName) {
      this.tab = tabName
    },
    /**
     * 获取实体参数
     * @param {Object} item - 参数项
     * @returns {Object} 实体参数对象
     */
    getEntityParam(item) {
      return this.$refs.formTemp?.getEntityParam({
        ...item,
        targetKey: 'submit',
        customId: this.customId,
      })
    },

    /**
     * 提交流程后的操作
     * 可在此处添加提交成功后的业务逻辑
     */
    afterSubmit() {},

    /**
     * 表单退回之后的操作
     * 可在此处添加退回后的业务逻辑
     */
    afterReject() {},

    /**
     * 提交流程前钩子函数
     * 在提交前执行表单的beforeSubmit方法
     */
    beforeSubmit() {
      this.$refs.formTemp?.beforeSubmit()
    },

    /**
     * 非空校验方法
     * 调用业务表单提供的校验方法
     * @returns {boolean} 校验结果
     */
    checkRules() {
      // 业务表单提供校验方法
      if (this.$refs.formTemp.validator) {
        return this.$refs.formTemp.validator()
      } else {
        return true
      }
    },

    /**
     * 设置提交按钮文本
     * @param {string} submitText - 提交按钮文本
     */
    setSubmitText(submitText) {
      this.submitText = submitText
    },
  },
  watch: {
    /**
     * 监听当前按钮键变化
     * 当按钮键变化且需要通知时，显示通知确认框
     */
    currentButtonKey: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.isNotifyButtonKey) {
          this.getNotification(newVal).then(res => {
            if (res === false) {
              this.$refs.notificationRef.open({buttonKey: newVal, checked: res});
            }
          })
        }
      }
    }
  },

  /**
   * 组件挂载后初始化
   */
  mounted() {
    this.init()
  }
}
</script>

<style scoped lang="less">
@import './mobile-view';
</style>
