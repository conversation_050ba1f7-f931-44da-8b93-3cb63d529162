<template>
  <div class="main-container" style="height: 100%">
    <!-- 顶部导航栏 -->
    <fm-nav-bar
      :border="false"
      :left-arrow="hasLeftArrow"
      left-text=""
      right-text=""
      @click-left="goBack"
    >
      <template slot="title">
        <!-- 标题区域：显示表单名称和提示图标 -->
        <div class="flex col-baseline">
          <span>{{ formName }}</span>
          <!-- 根据按钮权限显示提示图标 -->
          <i v-if="isNotifyButtonKey" class="m-l-16 fks-icon-info-outline" style="color: #999999;font-size: 16px" @click="handleTipClick" />
        </div>
      </template>
    </fm-nav-bar>

    <!-- 标签页导航：仅在非添加模式下显示 -->
    <fm-tabs v-if="showTabs" line-width="80px" @tab-click="tabClick" v-model="tab">
      <template v-for="(item, index) in visibleTabTitles">
        <fm-tab :key="index" :label="item" :name="item"></fm-tab>
      </template>
    </fm-tabs>

    <!-- 项目关闭提示：当项目已关闭时显示错误提示 -->
    <fks-alert
      v-if="isClosed"
      title="项目已关闭，无法发起派车申请"
      type="error"
      style="width: unset;margin: 0 15px"
    />

    <!-- 添加/草稿模式主内容区域 -->
    <div
      v-if="isAddOrDraftMode"
      class="component task-detail-form"
    >
      <!-- 动态表单组件：根据业务类型加载对应的表单 -->
      <component
        :is="dynamicForm"
        ref="formTemp"
        v-bind="dynamicFormProps"
        v-on="dynamicFormEvents"
      />

      <!-- 添加/草稿模式的审批按钮：仅在详情标签页显示 -->
      <approve-buttons
        v-if="showApproveButtonsInTab"
        v-bind="approveButtonsProps"
        v-on="approveButtonsEvents"
      />

      <!-- 添加/草稿模式的时间线：仅在流转标签页显示 -->
      <div v-show="tab === 1" class="content-container">
        <flow-timeline v-bind="timelineProps"></flow-timeline>
      </div>
    </div>
    <!-- 任务执行模式主内容区域 -->
    <div v-else :style="executeContainerStyle">
      <!-- 执行模式的表单内容：仅在详情标签页显示 -->
      <div
        v-show="tab === 0"
        class="content-container x2"
        :style="formContainerStyle"
      >
        <component
          :is="dynamicForm"
          ref="formTemp"
          v-bind="dynamicFormProps"
          v-on="dynamicFormEvents"
        />
      </div>

      <!-- 执行模式的审批按钮：仅在详情标签页显示 -->
      <approve-buttons
        v-if="showApproveButtonsForExecute"
        v-bind="approveButtonsProps"
        v-on="approveButtonsEvents"
      />
    </div>

    <!-- 流转标签页内容：显示流程时间线 -->
    <div v-show="tab === 1" class="content-container">
      <flow-timeline ref="timelineRef" v-bind="timelineProps"></flow-timeline>
    </div>

    <!-- 审批标签页内容：显示审批表单和操作按钮 -->
    <div v-show="showApprovalTab" class="content-container">
      <flow-approval
        ref="flowApproval"
        v-bind="flowApprovalProps"
      />

      <!-- 审批标签页的审批按钮 -->
      <approve-buttons
        v-if="showApproveBtn"
        v-bind="approveButtonsProps"
        v-on="approveButtonsEvents"
      />
    </div>

    <!-- 委托标签页内容：显示委托相关信息 -->
    <div v-show="tab === 3" class="content-container">
      <flow-entrust :formData="formData"></flow-entrust>
    </div>

    <!-- 消息通知标签页内容：显示消息通知相关信息 -->
    <div v-show="tab === 4" class="content-container">
      <div class="notification-placeholder">
        <h3>消息通知</h3>
        <p>这里是消息通知内容区域</p>
        <p>当满足以下条件时显示此标签页：</p>
        <ul>
          <li>流程类型为execute</li>
          <li>taskKey大于UserTask__200</li>
          <li>当前用户是车辆调度员</li>
        </ul>
      </div>
    </div>

    <!-- 通知确认弹窗组件 -->
    <notification-confirm-mobile ref="notificationRef" />
  </div>
</template>
<script>
import mixins from './mixins'
import * as StateTypes from '@store/State/stateTypes'
import { getApprovalList } from './api'
import storage from '@utils/storage'
import flowTimeline from './components/FlowTimeline/index.vue'
import flowEntrust from './components/FlowEntrust/index.vue'
import flowApproval from './components/FlowApproval/index.vue'
import CarApply from '@modules/FormCenter/CarApply/index.vue'
import ApproveButtons from '@modules/FormCenter/components/ApproveButtons/index.vue'
import NotificationConfirmMobile from '@components/NotificationConfirm/mobile-view.vue'
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'
import { changeTips } from '@utils/constants'
import { mapState } from 'vuex'

// Constants
const MODIFY_BUTTON_KEYS = ['XC_DRIVER_CAR_MODIFY', 'XC_FORM_MODIFY', 'XC_CAR_COMP_MODIFY']
const EXCLUDED_BUTTON_KEYS = [...MODIFY_BUTTON_KEYS, 'FORM_MODIFY_ADMIN']
const TAB_NAMES = {
  DETAIL: '详情',
  FLOW: '流转',
  APPROVAL: '审批',
  ENTRUST: '委托',
  NOTIFICATION: '消息通知'
}
export default {
  name: 'FormCenterMobileView',
  components: {
    NotificationConfirmMobile,
    CarApply,
    flowApproval,
    flowEntrust,
    flowTimeline,
    ApproveButtons,
  },
  mixins: [mixins, isConfirmXCMixin],
  data() {
    return {
      tabTitles: [],
      tab: TAB_NAMES['DETAIL'],
      from: {},
      formData: {},
      isDefault: true, // true 使用原来的请求参数格式 false不是使用
      detailParamList: [], // 需提交的明细表数据
      submitText: '提交',
      buttonList: [],
      returnNodeList: [],
      isClosed: false
    }
  },
  computed: {
    ...mapState([StateTypes.PORTAL_STATUS_TABLE, StateTypes.IS_PROJECT_CLOSED]),
    // UI State Computed Properties
    // 判断是否需要显示返回箭头：当申请资源类型不等于1时显示
    hasLeftArrow() {
      return Boolean(this[StateTypes.APPLY_RESOURCE] !== 1)
    },

    // 是否显示标签页：添加模式不显示标签页
    showTabs() {
      return this.type !== 'add'
    },

    // 是否为添加或草稿模式：用于控制主内容区域的显示逻辑
    isAddOrDraftMode() {
      return this.type === 'add' || this.type === 'draft'
    },

    // Tab Management
    // 过滤可见的标签页：移除委托标签，并根据按钮权限过滤审批标签
    visibleTabTitles() {
      return this.tabTitles.filter(item => {
        // 始终隐藏委托标签
        if (item === TAB_NAMES.ENTRUST) return false
        // 如果是修改按钮权限，隐藏审批标签
        if (MODIFY_BUTTON_KEYS.includes(this.$route.query.buttonKey) && item === TAB_NAMES.APPROVAL) {
          return false
        }
        // 根据条件隐藏消息通知标签
        if (item === TAB_NAMES.NOTIFICATION && !this.shouldShowNotificationTab) {
          return false
        }
        return true
      })
    },

    // 是否显示消息通知标签页：需要满足三个条件
    // 2. taskKey大于UserTask__200
    // 3. 当前用户是车辆调度员
    shouldShowNotificationTab() {
      return this.isTaskKeyAfterUserTask200 &&
             this.currentUserIsDispatcher
    },

    // 当前用户是否为车辆调度员
    currentUserIsDispatcher() {
      // 从流程数据中获取车辆调度员信息
      if (this.comments && this.comments.length > 0) {
        const dispatcherTask = this.comments.find(
          item => item.taskKey === 'UserTask_200' || item.taskKey === 'UserTask_500'
        )
        if (dispatcherTask) {
          const username = storage.get('username')
          return dispatcherTask.assignee === username
        }
      }
      return false
    },

    // 判断taskKey是否大于UserTask__200
    isTaskKeyAfterUserTask200() {
      if (this.taskKey && this.taskKey.startsWith('UserTask_')) {
        const taskNumber = parseInt(this.taskKey.replace('UserTask_', ''))
        return taskNumber > 200
      }
      return false
    },

    // 在标签页中显示审批按钮：仅在详情标签页且需要显示按钮时
    showApproveButtonsInTab() {
      return this.tab === 0 && this.showApproveBtn
    },

    // 在执行模式下显示审批按钮：仅在详情标签页、执行模式且需要显示按钮时
    showApproveButtonsForExecute() {
      return this.tab === 0 && this.type === 'execute' && this.showApproveBtn
    },

    // 是否显示审批标签页：在第三个标签页(索引2)时显示，或在详情页时根据条件显示
    showApprovalTab() {
      return this.tab === 2 || (this.tab === 0 && (this.tabTitles.length === 2 ? !!this.$route.query.buttonKey : true))
    },

    // Style Computed Properties
    // 根据页面类型和状态计算内容区域高度
    getHeight() {
      // 编辑模式下，详情标签页且有按钮时，预留更多空间给按钮
      if (this.tab === 0 && this.type === 'edit' && this.buttonList.length > 0) {
        return 'calc(100% - 149px)'
      }
      // 查看模式高度较小，其他模式预留标准空间
      return this.type === 'view' ? 'calc(100% - 88px)' : 'calc(100% - 140px)'
    },

    // 执行模式下的容器样式：详情标签页时设置高度和滚动，其他情况无样式
    executeContainerStyle() {
      return this.tab === 0
        ? [{ height: this.getHeight }, { 'overflow-y': 'auto', 'overflow-x': 'hidden' }]
        : {}
    },

    // 表单容器样式：根据标签页数量调整底部边距
    formContainerStyle() {
      return { 'margin-bottom': this.tabTitles.length > 2 ? '0px' : 0 }
    },

    // Component Props Computed Properties
    // 动态表单组件的属性配置
    dynamicFormProps() {
      return {
        bizId: this.bizId,
        taskKey: this.taskKey,
        type: this.type,
        currentButtonKey: this.currentButtonKey,
        isCrossNodeReturn: this.isCrossNodeReturn,
        ...this.$attrs
      }
    },

    // 动态表单组件的事件监听
    dynamicFormEvents() {
      return {
        setEntityName: this.setEntityName,
        setFormName: this.setFormName,
        setModelKey: this.setModelKey,
        setPreSaveValidateProps: this.setPreSaveValidateProps,
        setService: this.setService,
        setSubmitText: this.setSubmitText,
        setReturnNodeList: this.setReturnNodeList,
        'init-success': this.handleInitSuccess
      }
    },

    // 审批按钮组件的属性配置
    approveButtonsProps() {
      return {
        'button-loading': this.buttonLoading,
        taskKey: this.taskKey,
        type: this.type,
        'biz-id': this.bizId,
        'current-button-key': this.currentButtonKey,
        'submit-text': this.submitText,
        'disable-submit': this.isClosed,
        getComment: this.getComment,
        getReturnNode: this.getReturnNode
      }
    },

    // 审批按钮组件的事件监听
    approveButtonsEvents() {
      return {
        onAction: this.onAction,
        getButtonList: this.getButtonList
      }
    },

    // 时间线组件的属性配置
    timelineProps() {
      return {
        bizId: this.bizId,
        taskId: this.taskId
      }
    },

    // 流程审批组件的属性配置
    flowApprovalProps() {
      return {
        afterSubmit: this.afterSubmit,
        aftereReject: this.afterReject,
        beforeSubmit: this.beforeSubmit,
        checkRules: this.checkRules,
        detailParamList: this.detailParamList,
        entityName: this.entityName,
        formData: this.formData,
        from: this.from,
        formName: this.formName,
        returnNodeList: this.returnNodeList,
        getEntityParam: this.getEntityParam,
        'is-default': this.isDefault,
        modelKey: this.flowData.modelKey,
        'submit-text': this.submitText,
        submitURL: this.service.submit,
        taskKey: this.taskKey,
        'biz-id': this.bizId,
        'show-btn': false,
        'is-show': this.getFlowApprovalShowState()
      }
    }
  },
  methods: {
    // Helper Methods
    getFlowApprovalShowState() {
      if (this.$route.query.buttonKey) {
        return !EXCLUDED_BUTTON_KEYS.includes(this.$route.query.buttonKey)
      }
      return this.tab === 2
    },

    handleInitSuccess() {
      this.showApproveBtn = true;
      this.$nextTick(() => {
        const projectId = this.$refs.formTemp.formData.projectId;
        if (projectId) {
          this.isClosed = this[StateTypes.PORTAL_STATUS_TABLE][projectId] === 200
        } else {
          this.isClosed = this[StateTypes.IS_PROJECT_CLOSED]
        }
      })
    },
    setReturnNodeList(list) {
      this.returnNodeList = list;
      // 获得lastItem，写入
      const lastItem = Array.isArray(list) && list.length > 0 ? list[list.length - 1] : null;
      this.$refs.flowApproval.setDefaultReturnNode(lastItem);
    },
    handleTipClick() {
      const content = changeTips[this.currentButtonKey];
      this.$dialog.alert({message: content});
    },
    getComment() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.comment : ''
    },
    getReturnNode() {
      return this.$refs.flowApproval ? this.$refs.flowApproval.returnedNode : ''
    },
    getButtonList(data) {
      this.buttonList = data
      this.formBtnList = data;
    },
    async init() {
      if (this.type !== 'execute') {
        this.tabTitles = [TAB_NAMES.DETAIL, TAB_NAMES.FLOW]
        console.log('11')
        return false
      }

      const params = {
        taskId: this.taskId,
        bizId: this.bizId,
      }

      if (!this.taskId && !this.bizId) {
        console.log('yy')
        return false
      }

      try {
        const res = await getApprovalList(params)
        const xx = res.data?.at(-1).taskKey === 'UserTask_0';
        console.info('🚀🚀', 'xx -->', xx, `<-- mobile-view.vue/init`)

        if (res.data?.at(-1).taskKey === 'UserTask_0') {
          this.tabTitles = [TAB_NAMES.DETAIL, TAB_NAMES.FLOW]
          this.taskKey = res.data[res.data.length - 1].taskKey
        } else {
          // 判断是否添加消息通知标签页
          const shouldAddNotification = this.shouldShowNotificationTab
          console.info('🚀🚀', 'shouldAddNotification -->', shouldAddNotification, `<-- mobile-view.vue/init`)
          if (shouldAddNotification) {
            this.tabTitles = [TAB_NAMES.DETAIL, TAB_NAMES.FLOW, TAB_NAMES.APPROVAL, TAB_NAMES.ENTRUST, TAB_NAMES.NOTIFICATION]
          } else {
            this.tabTitles = [TAB_NAMES.DETAIL, TAB_NAMES.FLOW, TAB_NAMES.APPROVAL, TAB_NAMES.ENTRUST]
          }
        }

        // Handle rejection and draft states
        this.handleSpecialStates(res.data)
      } catch (error) {
        console.error('Failed to initialize approval list:', error)
      }
    },

    handleSpecialStates(data) {
      const hasRejectState = data.length > 1 && data?.at(-2).approveState === 'reject'
      const hasDraftState = data.length === 1 &&
        data?.at(-1).taskKey === 'UserTask_0' &&
        data?.at(-1).approveState === 'stage'

      // Handle rejection or draft states if needed
      // Currently commented out as per original code
      if (hasRejectState && data?.at(-1).taskKey === 'UserTask_0') {
        // this.type = 'edit'
        // this.$route.params.type = 'edit'
      }

      if (hasDraftState) {
        // this.type = 'edit'
        // this.$route.params.type = 'edit'
      }
    },
    goBack() {
      if (this.type === 'add') {
        this.$router.push({ path: '/projectCar/projectPortal/carRecord' })
      } else {
        this.$router.go(-1)
      }
    },
    tabClick(val) {
      console.info('🚀🚀', 'val -->', val, `<-- mobile-view.vue/tabClick`)
      this.tab = val
    },
    getEntityParam(item) {
      return this.$refs.formTemp?.getEntityParam({
        ...item,
        targetKey: 'submit',
        customId: this.customId,
      })
    },
    // 提交流程后的操作
    afterSubmit() {},
    // 表单退回之后的操作
    afterReject() {},
    // 提交流程前钩子函数
    beforeSubmit() {
      this.$refs.formTemp?.beforeSubmit()
    },
    // 非空校验方法
    checkRules() {
      // 业务表单提供校验方法
      if (this.$refs.formTemp.validator) {
        return this.$refs.formTemp.validator()
      } else {
        return true
      }
    },
    setSubmitText(submitText) {
      this.submitText = submitText
    },
  },
  watch: {
    currentButtonKey: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.isNotifyButtonKey) {
          this.getNotification(newVal).then(res => {
            if (res === false) {
              this.$refs.notificationRef.open({buttonKey: newVal, checked: res});
            }
          })
        }
      }
    }
  },
  mounted() {
    console.log('执行init')
    this.init()
  }
}
</script>

<style scoped lang="less">
@import './mobile-view';
</style>
