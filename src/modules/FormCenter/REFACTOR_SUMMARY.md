# FormCenter Mobile View 重构总结

## 重构目标
1. 减少重复的组件调用
2. 简化复杂的条件判断
3. 优化坏品味的代码
4. 重构 fm-tab 的 tab-click 回调机制
5. 添加良好的注释

## 主要改进

### 1. 减少重复组件调用

**重构前问题：**
- `<component />` 组件在多个地方重复定义（添加模式、执行模式）
- `<approve-buttons>` 组件在 3 个不同位置重复使用
- 每个组件都有大量重复的属性和事件绑定

**重构后改进：**
- 统一使用一个 `<component />` 组件，通过计算属性控制显示
- 统一使用一个 `<approve-buttons>` 组件，通过计算属性控制显示和属性
- 使用 `v-bind` 和 `v-on` 简化属性和事件绑定

### 2. 简化条件判断

**重构前问题：**
```vue
<!-- 复杂的内联条件判断 -->
<fm-tab
  v-if="
    item !== '委托' &&
    (['XC_DRIVER_CAR_MODIFY', 'XC_FORM_MODIFY', 'XC_CAR_COMP_MODIFY'].includes(
      $route.query.buttonKey
    )
      ? item !== '审批'
      : true)
  "
/>
```

**重构后改进：**
```vue
<!-- 使用计算属性简化条件 -->
<fm-tab v-for="(item, index) in visibleTabTitles" />
```

### 3. Tab 切换机制重构

**重构前问题：**
- 使用数字索引（tab === 0, 1, 2）进行判断
- fm-tab 组件没有 name 属性，回调返回数字

**重构后改进：**
- 为 fm-tab 添加 `:name="item"` 属性
- 使用字符串常量替代数字索引
- 定义 TAB_NAMES 常量提高可维护性

```javascript
const TAB_NAMES = {
  DETAIL: '详情',
  FLOW: '流转',
  APPROVAL: '审批',
  ENTRUST: '委托'
}
```

### 4. 计算属性优化

**新增计算属性：**
- `mainContentClass` - 主内容区域CSS类
- `mainContentStyle` - 主内容区域样式
- `shouldShowForm` - 是否显示表单
- `formContainerClass` - 表单容器CSS类
- `formContainerStyle` - 表单容器样式
- `shouldShowApproveButtons` - 统一的按钮显示逻辑
- `formComponentProps` - 表单组件属性
- `formComponentEvents` - 表单组件事件
- `flowApprovalProps` - 审批流程组件属性
- `approveButtonsProps` - 审批按钮组件属性
- `approveButtonsEvents` - 审批按钮组件事件

### 5. 代码注释改进

**模板注释：**
- 为每个主要区域添加了清晰的注释
- 说明组件的显示条件和用途

**计算属性注释：**
- 为每个计算属性添加了 JSDoc 风格的注释
- 说明属性的用途和返回值

**方法注释：**
- 为每个方法添加了详细的注释
- 包含参数说明和返回值说明

## 代码量对比

**重构前：**
- 模板部分：~170 行
- 重复的 component 组件：2 个
- 重复的 approve-buttons 组件：3 个
- 复杂的内联条件判断：多处

**重构后：**
- 模板部分：~70 行（减少约 60%）
- 统一的 component 组件：1 个
- 统一的 approve-buttons 组件：1 个
- 清晰的计算属性：替代内联判断

## 功能保证

✅ 所有原有功能保持不变
✅ Tab 切换逻辑正常工作
✅ 表单显示和隐藏逻辑正确
✅ 审批按钮显示逻辑正确
✅ 代码通过 ESLint 检查

## 维护性提升

1. **可读性**：代码结构更清晰，注释完善
2. **可维护性**：减少重复代码，修改时只需改一处
3. **可扩展性**：计算属性模式便于添加新功能
4. **调试友好**：统一的组件便于问题定位

## 使用建议

1. 如需修改表单组件属性，只需修改 `formComponentProps` 计算属性
2. 如需修改按钮显示逻辑，只需修改 `shouldShowApproveButtons` 计算属性
3. 如需添加新的 tab，在 `TAB_NAMES` 常量中添加并更新相关计算属性
4. 所有 tab 相关判断使用字符串常量而非数字索引

## 最新更新（删除委托 + 新增消息通知）

### 删除委托相关代码
1. **删除委托常量**：从 `TAB_NAMES` 中移除 `ENTRUST: '委托'`
2. **删除委托组件**：移除 `flowEntrust` 组件的导入和注册
3. **删除委托模板**：移除委托tab的显示逻辑和模板代码
4. **删除委托计算属性**：移除 `isEntrustTab()` 计算属性
5. **更新过滤逻辑**：从 `visibleTabTitles` 中移除委托过滤逻辑

### 新增消息通知功能
1. **新增消息通知tab**：在 `TAB_NAMES` 中保留 `NOTIFICATION: '消息通知'`
2. **新增显示逻辑**：
   - `shouldShowNotificationTab()` - 控制消息通知tab是否显示
   - `isNotificationTab()` - 判断当前是否为消息通知tab
   - `isCurrentUserVehicleDispatcher()` - 判断当前用户是否为车辆调度员（TODO）
   - `isTaskAfterDispatcherApproval()` - 判断流程是否在调度员派车之后

3. **显示条件**：
   - 当前用户必须是车辆调度员
   - 流程必须在调度员派车（UserTask_200）通过后显示

4. **TODO 项目**：
   - `isCurrentUserVehicleDispatcher()` 方法需要根据实际业务逻辑实现车辆调度员角色判断
   - 消息通知组件内容需要开发

### 任务节点顺序定义
```javascript
const taskOrder = {
  'UserTask_0': 0,     // 用车申请
  'UserTask_100': 100, // 领导审批
  'UserTask_200': 200, // 调度员派车
  'UserTask_300': 300, // 司机确认
  'UserTask_400': 400, // 行车日志填报
  'UserTask_500': 500  // 费用审批
}
```

### 需要后续实现的功能
1. **车辆调度员角色判断**：需要根据实际的用户角色系统实现
2. **消息通知组件**：需要开发具体的消息通知界面和功能
3. **消息通知数据获取**：需要实现消息通知相关的API调用
