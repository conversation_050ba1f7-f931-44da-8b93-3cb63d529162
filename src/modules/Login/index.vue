<template>
  <div
    :class="{'login-conatiner': applyResource !== 2, 'container': applyResource === 2}"
    style="max-width: 600px;margin: 0 auto;"
  >
    <div class="logo-container" v-if="!(applyResource === 2)">
      <div class="logo-pic"></div>
      <span class="logo-title">华智车管</span>
    </div>

    <template v-if="applyResource === 2">
      <div class="images">
        <img :src="require('@/assets/img/login/login-car.png')" class="car-bg"/>
        <img :src="require('@/assets/img/login/alarm-bg.png')" class="alarm-bg"/>
        <img :src="require('@/assets/img/login/location-pg.png')" class="location-bg"/>
        <img :src="require('@/assets/img/login/profile-bg.png')" class="profile-bg"/>
      </div>
      <div class="panel">
        <fm-form @failed="pass = false">
          <header>您好，欢迎使用</header>
          <fm-field
            class="field"
            clearable
            v-model="phone"
            name="手机号"
            type="tel"
            placeholder="请输入手机号"
            :rules="phoneRules"
            @focus="checkedField = 'phone'"
            @blur="checkedField = null"
            :class="checkedField == 'phone' ? 'field-active' : ''"
          >
<!--            <template #prefix-icon>-->
<!--              <svg-icon-->
<!--                v-if="checkedField == 'phone'"-->
<!--                icon-class="phone-checked"-->
<!--                class="news-img"-->
<!--              />-->
<!--              <svg-icon v-else icon-class="phone" class="news-img" />-->
<!--            </template>-->
          </fm-field>
          <div  v-show="showCode" class="captcha-field">
            <fm-field :maxlength="6" v-model="validateCode" class="field" placeholder="请输入验证码"/>
            <fm-button
              v-show="showCode"
              :disabled="disabledMsg"
              class="captcha-button"
              size="large" type="primary" @click="getVericationCode">
              {{ hint }}
            </fm-button>
          </div>
          <div class="login-btn-container">
            <fm-button
              class="submit-button"
              @click="register"
              block
              type="primary"
              native-type="submit"
              :disabled="disabledRegister"
              :loading="loading"
            >{{ $t('login.authentication') }}</fm-button
            >
          </div>
        </fm-form>
      </div>
    </template>
    <fm-form class="form-container"  v-else-if="applyResource !== 2">
      <fm-field
        class="field-username"
        clearable
        v-model="username"
        name="用户名"
        placeholder="请输入用户名"
        :rules="userRules"
        @focus="checkedField = 'user'"
        @blur="checkedField = null"
        :class="checkedField == 'user' ? 'field-active' : ''"
      >
        <template #prefix-icon>
          <svg-icon
            v-if="checkedField == 'user'"
            icon-class="user-checked"
            class="news-img"
          />
          <svg-icon v-else icon-class="user" class="news-img" />
        </template>
      </fm-field>
      <fm-field
        class="field-pwd"
        clearable
        v-model="password"
        :type="passwordType"
        name="密码"
        placeholder="请输入密码"
        :rules="pwdRules"
        @click-suffix-icon="viewPassword"
        @focus="checkedField = 'pwd'"
        @blur="checkedField = null"
        :class="checkedField == 'pwd' ? 'field-active' : ''"
      >
        <template #prefix-icon>
          <svg-icon
            v-if="checkedField == 'pwd'"
            icon-class="pwd-checked"
            class="news-img"
          />
          <svg-icon v-else icon-class="pwd" class="news-img" />
        </template>
        <template #suffix-icon v-if="showIcon">
          <fm-image
            v-if="passwordType == 'password'"
            class="suffix-icon"
            width="20"
            height="20"
            :src="require('@/assets/img/login/no-visible.png')"
          />
          <fm-image
            v-else
            class="suffix-icon"
            width="20"
            height="20"
            :src="require('@/assets/img/login/visible.png')"
          />
          <!-- <i class="fm-icon fm-icon-eye-o suffix-icon" v-if="passwordType == 'text'"></i>
          <i class="fm-icon fm-icon-closed-eye suffix-icon" v-else></i> -->
        </template>
      </fm-field>
      <div class="sub-menu">
        <fm-checkbox v-model="rememberMe" shape="square">{{
          $t('login.RememberPassword')
        }}</fm-checkbox>
        <!-- <span @click="forgetPassword">{{ $t('login.forgetPassword') }}</span> -->
      </div>
      <div class="login-btn-container">
        <fm-button
          class="login-btn"
          @click="onSubmit"
          block
          type="primary"
          native-type="submit"
          >{{ $t('login.login') }}</fm-button
        >
      </div>
    </fm-form>
  </div>
</template>
<script>
import {
  Button,
  NavBar,
  Form,
  Field,
  Image,
  Icon,
  Checkbox,
  Toast,
  Dialog
} from 'fawkes-mobile-lib'
import { SM4Encrypt } from '@/utils/encryption/sm4'
import { GRANT_TYPE } from '@/config/enums'
import { mapActions, mapGetters, mapState, mapMutations } from 'vuex'
import { APPLY_RESOURCE } from '@/store/State/stateTypes'
import { SET_TEMP_USER, IS_LOGIN, SET_USER_INFO } from '@/store/Mutation/mutationTypes'
import { USER_LOGIN, LOGIN_SUCCESS } from '@/store/Action/actionTypes'
import { eventBackButton, exitApp } from '@/utils/app'
import { getRequest } from '@/utils/wxapi.js'
import { register } from '@/utils/wxapi.js'
import {needFeishuAuth} from "@/api/carApply";
const TOTAL = 60
const BTN_TITLE = '获取验证码'
const INTERVAL = 1000 // 定时器执行间隔
export default {
  name: 'login',
  components: {
    [Button.name]: Button,
    [NavBar.name]: NavBar,
    [Form.name]: Form,
    [Field.name]: Field,
    [Image.name]: Image,
    [Icon.name]: Icon,
    [Checkbox.name]: Checkbox,
    [Dialog.name]: Dialog
  },
  data() {
    return {
      state: '',
      phone: '',
      pass: false,
      showCode: true,
      loading: false,
      validateCode: '',
      captchaKey: '',
      sendDisabled: false,
      hint: BTN_TITLE,
      username: '',
      password: '',
      errorParams: {},
      rememberMe: false,
      showIcon: true,
      passwordType: 'password',
      checkedField: null,
      phoneRules: [
        {
          required: true,
          message: '手机号不能为空',
          trigger: 'onBlur',
        },
        { validator: this.phoneValidator, message: '请输入正确手机格式' },
      ],
      userRules: [
        {
          required: true,
          message: '用户名不能为空',
          trigger: 'onBlur',
        },
      ],
      pwdRules: [
        {
          required: true,
          message: '密码不能为空',
          trigger: 'onBlur',
        },
      ],
    }
  },
  computed: {
    ...mapGetters(['tempUser']),
    ...mapState([APPLY_RESOURCE]),
    disabledRegister() {
      return this.loading || !this.phone || !this.phoneValidator(this.phone) || (this.showCode && !this.validateCode)
    },
    disabledMsg() {
      return this.sendDisabled || !this.phone || !this.phoneValidator(this.phone)
    },
  },
  watch: {
    username(newUsername) {
      // 清空用户名时密码也清空
      if (!newUsername) {
        this.password = ''
        this.showIcon = true
      }
    },
    password(newPassword) {
      // 清空密码时显示查看密码按钮
      if (!newPassword) {
        this.showIcon = true
      }
    },
  },
  mounted() {
    if (+this.applyResource === 2) {
      // this.state = getRequest().state;
    }
    this.rememberMe = this.$storage.get('rememberMe') === 'true'
    if (this.rememberMe) {
      this.username = this.$storage.get('username')
      this.password = this.$storage.get('password')
      this.showIcon = false
      this.passwordType = 'password'

      //指纹登录 回填账号密码后直接登录
      if (this.$storage.get('enableFinger') == 'true') {
        Fingerprint.show(
          {
            clientId: 'fawkes', //Android: Used for encryption. iOS: used for dialogue if no `localizedReason` is given.
            clientSecret: 'fawkes_secret', //Necessary for Android encrpytion of keys. Use random secret key.
            title: '指纹登录验证',
            disableBackup: true,
            cancelButtonTitle: '取消',
          },
          () => {
            this.onSubmit()
          },
          (err) => {}
        )
      }
    } else {
      this.username = this.tempUser?.username
      this.password = this.tempUser?.password
    }
    // 移除返回上一级路由事件，设置退出应用
    document.removeEventListener('backbutton', this.$back, false)
    document.addEventListener('backbutton', eventBackButton, false)
  },
  beforeDestroy() {
    this.$store.commit(SET_TEMP_USER, {
      password: this.password,
      username: this.username,
    })
    // 恢复默认的返回上一级路由响应事件
    document.removeEventListener('backbutton', eventBackButton, false)
    document.removeEventListener('backbutton', exitApp, false)
    document.addEventListener('backbutton', this.$back, false)
  },
  methods: {
    ...mapActions('CarApply',[
      'authRegister',
      'getRegisterSms',
      'getCurrentUser',
      'getEnum',
      'getFawkesSign'
    ]),
    ...mapActions([LOGIN_SUCCESS]),
    ...mapMutations([SET_USER_INFO]),
    getSignFawkes() {
      this.getFawkesSign(params).then(res => {

      })
    },
    getVericationCode() {
      if (!this.phoneValidator(this.phone)) {
        return false;
      }
      this.loading = true
      // this.state.split('-')[2]}
      // const userFullName =  this.state.slice(this.state.lastIndexOf('-') + 1);
      // const userFulluserFullNameName = this.$storage.get('userFullname');
      this.getRegisterSms({
        phone: this.phone,
        openId: getRequest().openId,
        source: this[APPLY_RESOURCE]
      }).then((res) => {
        if (!res.status) {
          Dialog({message: res.message})
          return false;
        }
        this.$toast(`已发送验证码至${this.phone}`)
        this.captchaKey = res.data;
        this.sendDisabled = true
        let timeLeft = TOTAL
        const timer = setInterval(() => {
          if (timeLeft <= 0) {
            this.hint = BTN_TITLE
            this.sendDisabled = false
            clearInterval(timer)
          } else {
            this.hint = `${timeLeft}秒后获取`
          }
          timeLeft -= 1
        }, INTERVAL)
      }).finally(() => {
        this.loading = false;
      })
    },
    phoneValidator(val) {
      if (val) {
        return /^[1][23456789][0-9]{9}$/.test(val)
      }
      return true
    },
    async register() {
      if (!this.phone) {
        return Toast('手机号不能为空')
      }
      if (!this.phoneValidator(this.phone)) {
        return false;
      }
      // 验证用户是否绑定了飞书，有则继续，无则跳转
      const res =  await needFeishuAuth(this.phone)
      if (res.data) {
        await Dialog({message: '请前往飞书初始化系统！'})
        return false;
      }
      this.loading = true;
      this.$loading.show({ title: '' });
      const params ={
        openId: getRequest().openId,
        phone: this.phone,
        source: this[APPLY_RESOURCE]
      }
      if (this.showCode) {
        params.captchaCode = this.validateCode
        params.captchaKey = this.captchaKey
      }
      register(params).then(res => {
        this.loading = false;
        this.$loading.hide()
        if (!res.data && res.code === -8500010) {
          this.showCode = true
          return false
        }
      }).finally(() => {
        this.loading = false;
        this.$loading.hide()
      })
    },
    viewPassword() {
      if (this.passwordType == 'password') {
        this.passwordType = 'text'
      } else {
        this.passwordType = 'password'
      }
    },
   onSubmit() {
      if (!this.username) {
        return Toast('用户名不能为空')
      }
      if (!this.password) {
        return Toast('密码不能为空')
      }
      const loginData = this.getLoginForm()

      if (loginData.password && loginData.username) {
        this.$loading.show({ title: '' })
        this.$store
          .dispatch(USER_LOGIN, loginData)
          .then(async () => {
            this.$store.commit(IS_LOGIN, true)
            this.updataStorage()
          })
          .finally(() => {
            this.$loading.hide()
          })
      }
    },
    getLoginForm() {
      const form = {}
      form.grant_type = GRANT_TYPE.PASSWORD
      form.username = this.username
      // if (this.showIcon) {
      form.password = SM4Encrypt(this.password)
      // } else {
      //   form.password = this.password
      // }
      return form
    },
    // 登录成功时更新缓存
    updataStorage() {
      this.$storage.set('username', this.username)
      this.$storage.set('rememberMe', this.rememberMe)
      if (this.rememberMe) {
        this.$storage.set('password', this.password)
      } else {
        this.$storage.remove('password')
      }
      // 记录错误次数
      this.errorParams[this.username] = 0
      this.$storage.set('errorParams', JSON.stringify(this.errorParams))
    }
  },
}
</script>
<style lang="less" scoped>
@import "../Sign/index.less";
@import "./index.less";

</style>
