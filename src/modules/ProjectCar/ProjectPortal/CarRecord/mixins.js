import {mapActions, mapGetters, mapMutations, mapState} from 'vuex'
import * as GetterTypes from "@store/Getter/getterTypes";
import {Toast} from 'fawkes-mobile-lib'
import {fileReader} from '@utils/exportFile'
import {getCommonFlowList} from '../../../CarApply/components/api'

import RateDrawer from "@modules/CarApply/components/RateDrawer.vue";
import RevocationDrawer from '../../../CarApply/components/RevocationDrawer.vue'
import RatePopup from '../../../CarApply/components/RatePopup.vue'
import platform from "@/mixins/platform";
import {getCarInfo, getDriverInfo, getFlowButton1} from "@/api/carApply";
import dayjs from "dayjs";

export default {
  components: {
    RatePopup,
    RateDrawer,
    RevocationDrawer
  },
  mixins:[platform],
  data() {
    return {
      showFilter: false,
      evaluateScore: 0,
      evaluate: 1,
      activeKey: 0,
      value: '', //搜索文字
      formList: [],
      timeApplyChoose: [],
      timeChoose: [],
      statusChoose: [],
      useCarTypeChoose: [],
      timeList: [
        { value: 1, text: '近1个月', start: `${this.$dayjs(new Date().getTime() - 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: 3, text: '近3个月', start: `${this.$dayjs(new Date().getTime() - 3 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: 6, text: '近6个月', start: `${this.$dayjs(new Date().getTime() - 6 * 30 * 24 * 3600 * 1000).format('YYYY-MM-DD HH:mm:ss')}`, end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: this.$dayjs().format('YYYY'), text: '今年', start: this.$dayjs().format('YYYY') + '-01-01 00:00:00', end: this.$dayjs().format('YYYY-MM-DD HH:mm:ss') },
        { value: this.$dayjs().format('YYYY') - 1, text: this.$dayjs().format('YYYY') - 1, start: `${this.$dayjs().format('YYYY') - 1}-01-01 00:00:00`, end: `${this.$dayjs().format('YYYY') - 1}-12-31 23:59:59`},
        { value: this.$dayjs().format('YYYY') - 2, text: this.$dayjs().format('YYYY') - 2, start:  `${this.$dayjs().format('YYYY') - 2}-01-01 00:00:00`, end: `${this.$dayjs().format('YYYY') - 2}-12-31 23:59:59` }
      ],
      showStartApplyTime: false,
      showEndApplyTime: false,
      showStartTime: false,
      showEndTime: false,
      form: {
        pageNo: 1,
        pageSize: 10,
        // total: 0,
        // sort: 'desc', // 排序规则,示例值(desc)
        // searchValue: '', // 搜索条件【位置名称、位置详细地址】
        endTime: '',
        // address: '',
        // startApplyTime: '',
        startTime: '',
        processState: '', // 流程类型
        applyFormState: '', // 流程节点类型
        // endApplyTime: ''
      },
      isRefresh: false,
      finished: false,
      loading: false, // 下拉刷新时禁止无限加载
      bannerShow: true,
      curCar: {},
      rateType: 1,// 1、评价，2、未评价
      exportLoading: false,
      params: {}, // 用于筛选条件参数
      configs: [
        {prop: 'startAddress', label: '出发/目的地', type: 'input'},
        {prop: 'onTime', label: '出车时间', type: 'daterange'},
        {prop: 'tripTime', label: '行程完成时间', type: 'daterange'},
        {prop: 'status', label: '状态', type: 'select'},
        {prop: 'carResourceType', label: '车辆来源', type: 'select'},
        {prop: 'useCarType', label: '用车类型', type: 'select'},
        {prop: 'applyTime', label: '申请时间', type: 'daterange'},
      ]
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser', 'scrollTop']),
    ...mapState(['savedFilters', 'globalQueryParams']),
    ...mapGetters([GetterTypes.IS_NOT_PC]),
    optionConfigs() {
      return {
        status: this.applyFormState.map(item => ({label: item.value, value: item.key})),
        useCarType: this.useCarType.map(item => ({label: item.value, value: item.key})),
        carResourceType: this.carResources.map(item => ({label: item.value, value: item.key}))
      }
    },
    // tagTypeList() {
    //   return ['', 'success', 'danger', 'warning', 'danger', '', '', '', '', '', '', 'success', '', 'danger']
    // }
    applyFormState() {
      return this.enums?.ApplyFormStateEnums ?? [];
    },
    // 用车类型
    useCarType() {
      return this.enums?.UseCarTypeEnums ?? [];
    },
    // 车辆来源列表
    carResources() {
      return this.enums?.CarResourceTypeEnums ?? [];
    },
  },
  mounted() {
    this.isMobile && Toast.loading({
      message: '加载中...',
      forbidClick: true,
    });
    if (this.$route.query.refresh === 'true') {
      setTimeout(() => {
        this.onRefresh();
      }, 3000);
    } else {
      if (this.isMobile) {
        this.onRefresh()
      } else if (this.isPC) {
        if (Object.keys(this.savedFilters).length === 0) {
          this.onRefresh()
        }
      }
    }
  },
  activated() {
    // 页面激活时移除默认的返回上一级路由处理事件，设置退出应用事件
    document.removeEventListener('backbutton', this.$back, false)
    document.addEventListener('backbutton', eventBackButton, false)
    this.onLoad()
    console.log('activated carApply')
  },
  deactivated() {
    // 页面隐藏时移除退出应用事件，恢复默认的返回上一级路由的处理事件
    document.removeEventListener('backbutton', eventBackButton, false)
    document.removeEventListener('backbutton', exitApp, false)
    document.addEventListener('backbutton', this.$back, false)
    this.onRefresh()
    console.log('deactivated carApply')
  },
  methods: {
    ...mapActions('CarApply', [
      'getCarDispatchUser',
      'getCarCompany',
      'getCarList',
      'getDriverList',
      'getRemindTitle',
      'getRejectCause',
      'getCurrentUser',
      'getCarCompanyApprover',
      'getApplyPerson',
      'getFlowRevocation',
      'exportAllRecords'
    ]),
    formatDate(str) {
      return str ? dayjs(str).format('YYYY-MM-DD HH:mm') : '';
    },
    ...mapMutations('CarApply', ['SET_LAST_ROUTE', 'SET_SCROLL_TOP']),
    getResourceType(type) {
      const result = this.carResources.find(item => item.key === type)
      if (result) {
        return result.value
      }
    },
    handleQueryChange(params) {
      const {onTime, applyTime, tripTime, ...rest} = params;
      if (onTime) {
        rest.startTime = onTime[0] + ' 00:00:00';
        rest.endTime = onTime[1] + ' 23:59:59';
      }
      if (applyTime) {
        rest.startApplyTime = applyTime[0] + ' 00:00:00';
        rest.endApplyTime = applyTime[1] + ' 23:59:59';
      }
      if (tripTime) {
        rest.xcStartTime = tripTime[0] + ' 00:00:00';
        rest.xcEndTime = tripTime[1] + ' 23:59:59';
      }
      this.params = {
        ...rest,
        pageSize: this.form.pageSize,
        pageNo: this.form.pageNo
      };
    },
    handleExport() {
      this.exportLoading = true
      this.exportAllRecords(this.params).then(res => {
        fileReader(res)
      }).finally(() => {
        this.exportLoading = false;
      })
    },

    showBtn(item) {
      let flag = false
      if (item.vdAfUseCarPersonList && item.vdAfUseCarPersonList.length > 0) {
        const list = item.vdAfUseCarPersonList.find(it => it.ucPersonUserName === this.currUser.userName);
        flag = !!list;
      } else {
        flag = this.currUser.userName === item.applyUserName;
      }
      const isCarCompanyApproverUser = item.carCompanyApproverUserName === this.currUser.userName && [7].includes(item.applyFormUserState);  // 允许租车公司车辆调度员处理【行程开始】
      return item.vdDriverInfo && item.vdDriverInfo.driverPhone && ([8, 10].includes(item.applyFormUserState) || isCarCompanyApproverUser || item.applyFormState === 4) && flag && +item.processState  === 0;
    },
    showDeal(item) {
      let flag = false
      if (item.vdAfUseCarPersonList && item.vdAfUseCarPersonList.length > 0) {
        const list = item.vdAfUseCarPersonList.find(it => it.ucPersonUserName === this.currUser.userName);
        flag = !!list;
      } else {
        flag = this.currUser.userName === item.applyUserName;
      }
      const val = (item.applyFormUserState === 8 || item.applyFormState === 4) && +item.processState  === 0 && flag;
      return val
    },

    openRate(item, type) {
      this.curCar = item;
      this.rateType = +type
      this.$nextTick(() => {
        this.$refs.rateDrawer.open();
      })
    },
    sortHandler() {},
    onClear() {
      this.form = {
        pageNo: 1,
        pageSize: 10,
        total: 0,
        sort: 'desc', // 排序规则,示例值(desc)
        searchValue: '', // 搜索条件【位置名称、位置详细地址】
        endTime: '',
        address: '',
        startApplyTime: '',
        startTime: '',
        endApplyTime: ''
      }
      this.params = {};
      // 复用刷新接口
      this.onRefresh()
    },
    clearFilter() {
      this.timeApplyChoose = [];
      this.timeChoose = [];
      this.statusChoose = [];
      this.useCarTypeChoose = [];
      this.form.startApplyTime = '';
      this.form.endApplyTime = '';
      this.form.startTime = '';
      this.form.endTime = '';
    },
    onSearchFilter() {
      // const flag = this.timeApplyChoose.length === 0 && !this.form.startApplyTime && !this.form.endApplyTime &&
      // this.timeChoose.length === 0 && !this.form.startTime && !this.form.endTime &&
      // this.statusChoose.length === 0 && this.useCarTypeChoose.length === 0;
      // if (flag) {
      //   Toast({
      //     message: '您还没有选择筛选项！',
      //     duration: 3000
      //   });
      //   return false;
      // }
      this.onRefresh(true);
    },
    // 搜索
    onSearch(val) {
      // 复用刷新接口
      this.onRefresh()
    },
    getParams() {
      this.form.userName = this.currUser.userName || this.$storage.get('username');
      const {total, ...rest} = this.form;
      const form = JSON.parse(JSON.stringify(rest));
      if (form.startTime) {
        form.startTime = form.startTime + ' 00:00:00'
      }
      if (form.startApplyTime) {
        form.startApplyTime = this.form.startApplyTime + ' 00:00:00'
      }
      if (form.endTime) {
        form.endTime = form.endTime + ' 23:59:59'
      }
      if (form.endApplyTime) {
        form.endApplyTime = form.endApplyTime + ' 23:59:59'
      }
      if (this.timeApplyChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeApplyChoose[0]);
        form.startApplyTime = time.start;
        form.endApplyTime = time.end;
      }
      if (this.timeChoose.length > 0) {
        const time = this.timeList.find(it => it.value === this.timeChoose[0]);
        form.startTime = time.start;
        form.endTime = time.end;
      }
      if (this.statusChoose.length > 0) {
        form.status = this.statusChoose[0];
      }
      if (this.useCarTypeChoose.length > 0) {
        form.useCarType = this.useCarTypeChoose[0];
      }
      return {...this.globalQueryParams, ...form};
    },
    handleRefresh(){
      // 下拉刷新后，滚动高度重置为0
      this.SET_SCROLL_TOP(0)
      this.onRefresh()
    },
    query(flag) {
      this.loading = true
      if (flag) {
        // 筛选时，分页器需要选择第一页
        this.form.pageNo = 1;
      }

      getCommonFlowList({
        userName: this.currUser.userName || this.$storage.get('username'),
        pageNo: this.form.pageNo,
        pageSize: this.form.pageSize,
        ...this.params
      })
        .then(async (res) => {
          this.formList = await this.getData(res);
          this.loading = false;
          this.form.total = res.data.total;
        })
        .finally(() => {
        })
    },
    onRefresh(flag = false) {
      this.finished = false
      this.loading = true
      this.form.pageNo = 1;
      getCommonFlowList(this.getParams())
        .then(async (res) => {
          if (!res.status) {
            Toast({
              message: res.message || '请求失败',
              duration: 3000
            });
            return  false;
          }
          // if (flag && res.data.list.length === 0) {
          //   this.isMobile ? Toast({
          //     message: '未找到匹配的纪录\n请修改筛选条件试试',
          //     duration: 3000
          //   }) : '';
          //   return false;
          // }
          if (flag) {
            this.showFilter = false;
          }
          this.formList = res.data.list;
          this.formList = await this.getData(res);
          this.form.total = res.data.total;
          this.isRefresh = false
          if (res.data.isLastPage) {
            this.finished = true
            this.isMobile && (this.form.pageNo = 1)
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          Toast.clear()
          this.loading = false
        })
    },
    getContainer(id) {
      try {
        return document.getElementById(id);
      } catch (e) {
        return '#carApply';
      }
    },
    onSelect() {

    },
    onLoad() {
      this.loading = true;
      this.form.pageNo++
      getCommonFlowList(this.getParams())
        .then(async (res) => {
          if (!res.status) {
            this.finished = true
            return false;
          };
          const list = await this.getData(res);
          this.formList = [...this.formList, ...list]
          this.form.total = res.data.total;
          if (res.data.isLastPage) {
            this.finished = true
            this.isMobile && (this.form.pageNo = 1)
          }
        })
        .catch(() => {
          this.finished = true
        })
        .finally(() => {
          this.loading = false
        })
    },
    async getData(res) {
      const buttonIds = res.data.list.map(item => item.id).join(',')
      const carIds = res.data.list.filter(item => item.carId).map(item => item.carId).join(',')
      const driverIds = res.data.list.filter(item => item.driverId).map(item => item.driverId).join(',')
      const {data: carRes} = await getCarInfo(carIds)
      const {data: driverRes} = await getDriverInfo(driverIds);

      const response = await getFlowButton1(buttonIds)
      return res.data.list.map(item => {
        item.showPopover = false;
        item.buttonsBig = [];
        item.buttonsSmall = [];
        item.buttonsSmallMore = [];
        item.buttonsBig.push({
          buttonColor: '60,131,255',
          buttonDesc: '用户查看按钮',
          buttonKey: 'VIEW',
          buttonSizeType: 'big',
          buttonValue: '查看'
        });
        item.vdCarInfo = carRes ? carRes[item.carId] : null;
        item.vdDriverInfo = driverRes ? driverRes[item.driverId] : null;
        if (response.data[item.id].length) {
          response.data[item.id].forEach(button => {
            if (button.buttonSizeType === 'big') {
              item.buttonsBig.push(button)
            } else {
              item.buttonsSmall.push(button)
            }
          })
        }
        return item;
      });
    },
    setCurPage() {
      // this.SET_LAST_ROUTE(this.$route.path);
    },
    /*打开新增页面*/
    add() {
      this.setCurPage();
      this.$router.push({
        name: 'formAdd',
        params: {type: 'add', formKey: 'carApply', normal: false},
      })
    },

    // 撤销
    revocation(item) {
      // this.isMobile && Dialog.confirm({
      //   // 组件除show外的属性
      //   title: '提示',
      //   message: '是否要执行撤销操作?',
      // })
      //   .then(() => {
      //     this.flowRevocation(item)
      //   })
      //   .catch(() => {
      //   })
      // this.isPC && this.flowRevocation(item);
      this.flowRevocation(item);
    },
    flowRevocation(item) {
      this.curCar = item;
      this.$nextTick(() => {
        this.$refs.revocationDrawer.open();
      })
    },

  },
}
