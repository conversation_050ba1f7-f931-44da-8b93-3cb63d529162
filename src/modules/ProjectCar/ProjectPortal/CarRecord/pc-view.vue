<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">用车记录</div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      :current-page.sync="pageNo"
      :showButtons="showButtons"
      :show-selection="false"
      @searchData="searchData"
      @export-data="handleExport"
    >
      <template v-slot:column-useCarMatter="{scope}">
        {{ getUseCarMatter(scope) }}
      </template>
      <template v-slot:column-startTime="{scope}">
        {{ getStartTime(scope) }}
      </template>
      <template v-slot:column-processState="{scope}">
        <card-tag v-if="scope.row.processState" class="p-24 p-t-8 p-b-8" :tag="{color: getTagType(scope.row.processState), text: getTagText(scope.row, 'processState')}" />
      </template>
      <template v-slot:column-operation="{scope}">
        <div class="flex col-center" style="flex-wrap: nowrap">
          <div style="color: #3C83FF;margin-right: 10px" class="cursor-pointer" @click.stop="handleDetails(scope)">查看</div>
          <!-- 等于1表示行程已完成，且没有评价过的才可以评价           -->
          <fks-popover
            v-if="scope.row.processState === '1' && !scope.row.hasRated"
            v-model="scope.row['popoverVisible']"
            placement="left"
            width="340"
            trigger="manual"
            :key="`popoverRef-${scope.$index}`"
            :ref="`popoverRef-${scope.$index}`"
          >
            <rater
              :row="scope.row"
              :type="scope.row['hasRated'] ? 'view' : 'edit'"
              :visible="scope.row['popoverVisible']"
              :suggestions="suggestions"
              @visible="scope.row['popoverVisible'] = false"
              @updateView="handleUpdateView(scope)"
            />
            <span
              slot="reference"
              style="color: #3C83FF"
              class="cursor-pointer"
              @click.stop="handleRate(scope)"
            >
            {{scope.row['hasRated'] ? '查看评价' : '评价'}}
        </span>
          </fks-popover>
          <!--  暂存的表单可删除          -->
          <fks-popconfirm
            v-if="scope.row.processState === '3' || scope.row.processState === '2'"
            title="确认删除？"
            @onConfirm="handleDelete(scope)"
          >
            <div class="cursor-pointer" style="color: #FF4D4F;margin-right: 10px" slot="reference">删除</div>
          </fks-popconfirm>
        </div>
      </template>
    </TempTable>
    <fks-drawer
      :before-close="beforeClose"
      :visible.sync="visible"
      :wrapperClosable="true"
      :modal="false"
      class="dialog"
      direction="rtl"
      size="600px"
    >
      <header class="card-header drawer-header" slot="title" style="display: flex; justify-content: space-between; align-items: center">
        <div class="flex col-center m-b-24">
          <div class="drawer-title" style="color: #333333">
            用车详情
          </div>
          <div class="m-l-20">
            <card-tag class="m-l-10" :tag="{ color: '#3C83FF', text: currentData.projectName }" />
          </div>
        </div>

        <div v-loading="btnLoading" class="print-button" @click="doPrint" style="height: 16px;display: flex;align-items: center;gap: 2px;">
          <img src="@/assets/img/application/print.svg" alt="打印" width="16" height="16" class="print-icon" />
          <span class="print-label" style="font-size: 14px;">打印</span>
        </div>
      </header>
      <TripForm
        v-if="visible"
        v-bind="currentData"
        style="margin: 0;height: 100%;overflow: auto"
      />
    </fks-drawer>

    <fks-dialog
      :visible.sync="dialogVisible"
      title="用车记录预览"
      append-to-body
      class="dialog-8vh"
    >
      <div style="height: calc(75vh - 123px); overflow-y: hidden;">
        <div class="pdf_frame"  style="height: 100%">
          <iframe  id="pdf_frame" name="pdf_frame" :src="pdfBlobUrl"></iframe>
        </div>
      </div>
      <template #footer>
        <fks-button
          style="border-radius: 4px;background: #5483F7;box-shadow: 3px 3px 6px 0px rgba(84, 131, 247, 0.4);
                 font-size: 14px;font-weight: normal;line-height: normal;color: #FFFFFF;"
                     type="primary" @click="downloadPdf">下载</fks-button>
      </template>
    </fks-dialog>
  </div>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import Rater from './components/exclude/rater.vue';
import { mapGetters, mapState } from 'vuex'
import PreView from '@components/PreView/index.vue'
import {
  exportFlowData,
  exportUseCarRecord,
  getUseCarListByPost,
  getUseCarParam
} from '@modules/CarApply/components/api'
import * as StateTypes from '@store/State/stateTypes'
import {getCarInfo, getDriverInfo} from '@/api/carApply'
import statusTagMixin from "@/mixins/statusTagMixin";
import {
  deleteCarRecord, exportPdf,
  getRateContent,
  getRateList
} from '@modules/ProjectCar/ProjectPortal/CarRecord/api'
import storage from "@utils/storage";
import TitleKit from "@components/PageTitle/TitleKit.vue";
import globalSearchMixin from "@/mixins/globalSearchMixin";
import TripForm from '@/modules/FormCenter/vehicleDispatch/pc-view.vue';
import CardTag from "@components/CardFlow/components/tag.vue";
import { fileReader } from '@utils/exportFile'
import RevocationDrawer from "@modules/CarApply/components/RevocationDrawer.vue";
import EventBus from "@utils/eventBus";
import * as GetterTypes from '@store/Getter/getterTypes'
import { ButtonKeys, FORM_VIEW_PARAMS } from '@store/modules/FormController/formFieldConfig'
import { getUserTaskByFormId } from '@modules/Todo/api'


export default {
  name: 'CarRecord',
  mixins: [statusTagMixin, globalSearchMixin],
  components: {
    RevocationDrawer,
    CardTag,
    TitleKit,
    TempTable,
    Rater,
    TripForm,
    PreView
  },
  data() {
    return {
      suggestions: {},
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['filter', 'export'],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      passengerInfo: null,
      visible: false,
      tableData: [],
      currentData: {},
      revocationData: null,
      type: 'view',
      selectedFields: [
      ],
      tableConfig: [
        {
          label: "项目名称",
          prop: 'projectName',
          width: '150px',
        },
        {
          label: '行程编号',
          prop: 'formNum',
          width: '200px',
        },
        {
          label: "用车事由",
          prop: 'useCarMatter',
          customer: true
        },
        {
          label: "出发地",
          prop: 'startAddress',
          width: '200px',
        },
        {
          label: "目的地",
          prop: 'endAddress',
          width: '200px',
        },
        {
          label: "用车时间",
          prop: 'startTime',
          width: '160px',
          customer: true
        },
        {
          label: "车牌号",
          prop: 'carNum',
          width: '150px',
        },
        {
          label: "司机",
          prop: 'driverFullName',
        },
        {
          label: "流程状态",
          prop: 'processState',
          group: '行程信息',
          width: '200px',
          customer: true
        },
        {
          label: '操作',
          prop: 'operation',
          fixed: 'right',
          width: '120px',
          customer: true
        }
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      dialogVisible: false,
      pdfBlobUrl: "",
      downloadFileName: "",
      btnLoading: false,
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
  },
  async created() {
    this.queryParams.projectId = this.portal.id;
    getUseCarParam().then(res => {
      this.searchConfigs = res.data;
    })
    this.selectedFields = this.tableConfig.filter(item => {item.prop})
    const res = await getRateContent();
    if (res.status) {
      this.suggestions = res.data.reduce((acc, item) => {
        acc[item.score] = item.contents.split('、');
        return acc;
      }, {});
    }
  },
  mounted() {
    this.getData(this.queryParams)
  },
  methods: {
    downloadPdf() {
      if (!this.pdfBlobUrl || !this.downloadFileName) {
        this.$message.warning("文件未加载完成");
        return;
      }

      const link = document.createElement('a');
      link.href = this.pdfBlobUrl;
      link.download = this.downloadFileName; // ✅ 设置你想要的文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    getFileNameFromHeader(contentDisposition) {
      if (!contentDisposition) return '未命名文件.pdf';
      const match = /filename\*?=(?:UTF-8'')?([^;\n]*)/.exec(contentDisposition);
      if (match && match[1]) {
        return decodeURIComponent(match[1].replace(/['"]/g, ''));
      }
      return '用车记录.pdf';
    },
    doPrint() {
      if (this.btnLoading) return;
      let id = this.currentData.bizId
      this.btnLoading = true;

      const loadingMessage = this.$message({
        message: '正在加载打印文件...',
        type: 'info',
        duration: 0, // 不自动关闭
        showClose: true
      });
      exportPdf(id)
        .then(res => {

          const disposition = res.headers['content-disposition'];
          const filename = this.getFileNameFromHeader(disposition);

          const blobUrl = URL.createObjectURL(res);
          const iframe = document.getElementById('pdf_frame');
          if (iframe) {
            iframe.src = blobUrl;
          }
          this.pdfBlobUrl = blobUrl;
          this.downloadFileName = filename;
          this.dialogVisible = true;
        })
        .catch(err => {
          this.$message.error('加载失败，请稍后重试');
          console.error(err);
        })
        .finally(() => {
          this.btnLoading = false;
          loadingMessage.close(); // 手动关闭提示
        });
    },
    getTagType(value) {
      let status = {
        0: '#1790fe',
        1: '#40bb5a',
        2: '#ff4d4f',
        3: '#909399',
        10000: '#909399',
      };
      return status[value];
    },
    globalSearch() {
      this.tableData = [];
      this.reSearchData();
    },
    handleUpdateView(scope) {
      this.reSearchData();
      scope.row['popoverVisible'] = false;
    },
    handleDelete(scope) {
      deleteCarRecord(scope.row.id).then(res => {
        if (res.status) {
          this.$message.success('删除成功');
          // 刷新待办红点
          EventBus.$emit('refreshDot', 'Todo');
          this.reSearchData();
        }
      })
    },
    async handleDetails(scope) {
      let taskKey = ButtonKeys.VIEW;
      if (scope.row.applyFormState !== 1) {
        const res = await getUserTaskByFormId(scope.row.id);
        if (res.status && res.data && res.data[scope.row.id]) {
          taskKey = res.data[scope.row.id].taskKey;
        }
      }
      this.currentData = {...FORM_VIEW_PARAMS, bizId: scope.row.id, type: 'view', noAuth: true, projectName: scope.row.projectName, taskKey: taskKey}
      this.visible = true;
    },
    handleRate(scope) {
      scope.row['popoverVisible'] = true;
      for (const key in this.$refs) {
        this.$refs[key] && this.$refs[key].doClose();
      }
      this.$nextTick(() => {
        const el = this.$refs[`popoverRef-${scope.$index}`].$refs.popper;
        el.previousSibling.style.display = 'none'
      })
    },
    getUseCarMatter(scope) {
      const enums = JSON.parse(localStorage.getItem('enums'));
      if (enums && enums.UseCarMatterEnums && scope.row.useCarMatter) {
        return enums.UseCarMatterEnums.find(item => item.key.toString() === scope.row.useCarMatter.toString()).value;
      }
      return '';
    },
    getStartTime(scope) {
      return scope.row.startTime ? this.$dayjs(scope.row.startTime).format('YYYY-MM-DD HH:mm') : '/';
    },
    reSearchData(refresh) {
      if (!refresh || refresh == false) {
        this.pageNo = 1
      }
      this.getData(this.queryParams)
    },
    handleExport(list) {
      list = list.filter(item => {
        return item.fieldName !== '操作';
      })
      let queryParam = {
        ...this.queryParams,
      }
      let data = {
        pageGetParam: queryParam,
        fields: list
      }
      exportFlowData(data).then(res => {
        try {
          fileReader(res)
        } catch (error) {
          console.error("导出失败:", error);
          alert("导出失败，请稍后重试。");
        }
      })
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.loading = true
      this.queryParams = { ...this.queryParams, ...query }
      return getUseCarListByPost({
        ...this.queryParams,
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        userName: this.currUser.userName || this.$storage.get('username'),
      }, this[GetterTypes.GET_GLOBAL_STATE]).then(async (res) => {
        if (res.status) {
          const {total, isLastPage} = res.data || [];
          const list = await this.fetchOtherData(res)
          this.total = total;
          this.finished = isLastPage;
          this.tableData = [...list];
        }
      }).finally(() => {
        this.loading = false;
      });
    },

    async fetchOtherData(res) {
      const {list, total, isLastPage} = res.data || [];
      this.finished = isLastPage;
      const carIds = res.data.list.filter(item => item.carId).map(item => item.carId).join(',')
      const driverIds = res.data.list.filter(item => item.driverId).map(item => item.driverId).join(',')
      const ids = res.data.list.filter(item => item.id).map(item => item.id).join(',')
      const {data: carRes} = await getCarInfo(carIds)
      const {data: driverRes} = await getDriverInfo(driverIds);
      const {data: rateRes} = await getRateList({ids: ids, userName: storage.get('username')})
      this.total = total;
      return list.map(item => {
        if (carRes && carRes[item.carId]) {
          const {carNum, id} = carRes[item.carId];
          item.carNum = carNum; // 车牌号
          item.vdCarInfoId = id;
        } else {
          item.carNum = ''; // 车牌号
        }

        if (driverRes && driverRes[item.driverId]) {
          const {driverFullName, id} = driverRes[item.driverId];
          item.driverFullName = driverFullName; // 司机名称
          item.vdDriverInfoId = id;
        } else {
          item.driverFullName = ''; // 司机名称
        }

        if (rateRes && rateRes[item.id]) {
          item.hasRated = true;
        } else {
          item.hasRated = false;
        }

        // popover visible
        item.popoverVisible = false;
        return item;
      })
    },
    formatDate(row, column) {
      if (!value) return '' // 如果值为空，返回空字符串
      const date = new Date(value)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}` // 返回格式化后的日期字符串 "YYYY-MM-DD"
    },
    beforeClose(done) {
      this.visible = false
      done()
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
  },
}
</script>

<style scoped lang="less">
@import '~@/styles/headers';

.print-button {
  margin-right: 24px;
  padding: 24px 12px;
  cursor: pointer;
  user-select: none;
  color: #555555;
  border-radius: 6px;
}

.print-button:hover {
  background-color: #D8D8D8;
}

#pdf_frame {
  display: block;
  width: 100%;
  height: 100%;
  border: none;
  margin: 0;
}
</style>
