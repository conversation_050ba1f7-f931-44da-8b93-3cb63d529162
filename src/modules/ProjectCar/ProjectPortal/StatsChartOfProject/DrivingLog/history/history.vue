<template>
  <fks-popover
    v-if="!disableMutation"
    placement="bottom"
    :width="popoverWidth"
    trigger="manual"
    v-model="visible"
  >
<!--    <fks-button-->
<!--      v-if="!disableMutation"-->
<!--      slot="reference"-->
<!--      style="color: #3c83ff"-->
<!--      class="second-text"-->
<!--      text-->
<!--      icon="fks-icon-download"-->
<!--      @click="visible = true"-->
<!--      >快捷发起</fks-button-->
<!--    >-->

    <div id="tableComponent" class="full-width full-height bg-white">
      <div class="flex col-center row-between" style="margin-bottom: 20px">
        <div style="font-weight: bold;font-size: 18px">导入历史行程</div>
        <i class="cursor-pointer fks-icon-close" @click="visible = false"/>
      </div>
      <div class="table-header" id="tableHeader">
        <div style="width: 250px; margin: 8px 2px">
          <fks-input
            v-model="searchValue"
            clearable
            placeholder="请输入关键字搜索"
            @keyup.enter.native="handleSearch"
            @clear="handleSearch"
          >
            <i slot="prefix" style="line-height: 32px" class="fks-input__icon fks-icon-search"></i>
          </fks-input>
        </div>
      </div>
      <!-- 表格 -->
      <div class="table-body draggable">
        <historyTable
          ref="historyTableRef"
          :table-data="tableData"
          :table-config="tableConfig"
          :loading="loading"
          :selected-rows="selectedRows"
          @row-select="handleRowSelect"
          @column-click="handleClickRow"
        >
          <!-- 自定义操作列内容 -->
          <template v-slot:column-option="{ scope }">
            <fks-button @click="showDetail(scope.row)" type="text" size="small">查看</fks-button>
          </template>
        </historyTable>
      </div>
      <div
        class="full-width table-footer"
        id="pagination"
        style="display: flex; justify-content: flex-end; margin: 16px auto"
      >
        <fks-pagination
          bordered
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNo"
          :page-sizes="[5, 10, 20, 50, 200]"
          :page-size="pageSize"
          layout=" prev, pager, next, sizes, jumper"
          :total="total"
        >
        </fks-pagination>
      </div>
    </div>
    <fks-dialog title="行程详情" :visible.sync="detailVisible" :before-close="onCancel">
      <component
        v-if="detailVisible"
        v-bind="currentData"
        :is="CardView"
        class="flex-grow-1 overflow-y-auto"
        style="height: 500px"
      />
    </fks-dialog>

    <span class="dialog-footer full-width" style="display: flex; justify-content: flex-end">
      <fks-button @click="visible = false">取 消</fks-button>
      <fks-button type="primary" @click="handleConfirm">导 入</fks-button>
    </span>
  </fks-popover>
  <fks-tag v-else type="danger">项目已关闭，无法发起派车申请</fks-tag>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { getCommonFlowList, getUseCarListByPost } from '@modules/CarApply/components/api'
import * as StateTypes from '@store/State/stateTypes'
import * as Getters from '@store/Getter/getters'
import { getCarInfo, getFeeInfo, getPassengerList } from '@/api/carApply'
import historyTable from '@modules/ProjectCar/ProjectPortal/StatsChartOfProject/DrivingLog/history/HistoryTable.vue'
import CardView from '@/modules/FormCenter/vehicleDispatch/pc-view.vue'
import * as GetterTypes from '@store/Getter/getterTypes'
export default {
  name: 'CompanyIndex',
  components: { CardView, historyTable },
  data() {
    return {
      CardView,
      loading: false,
      pageNo: 1,
      pageSize: 5,
      total: 0,
      showButtons: [],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      passengerInfo: null,
      feeInfo: null,
      carInfo: null,
      visible: false,
      detailVisible: false,
      tableData: [],
      searchValue: '',

      windowWidth: window.innerWidth,
      selectedRows: [],
      tableHeight: 0,
      currentData: {},
      type: 'view',
      selectedFields: [],
      tableConfig: [
        {
          label: '用车事由',
          prop: 'useCarMatter',
          group: '用车信息',
          width: 80,
        },
        {
          label: '行程编号',
          prop: 'formNum',
          width: '200px',
        },
        {
          label: '出发地',
          prop: 'startAddress',
          group: '行程信息',
          width: '180px',
        },
        {
          label: '目的地',
          prop: 'endAddress',
          group: '行程信息',
          width: '180px',
        },
        {
          label: '出车时间',
          prop: 'startTime2',
          group: '行程信息',
          width: '158px',
        },
        {
          label: '返回时间',
          prop: 'endTime2',
          group: '行程信息',
          width: '158px',
        },
        {
          label: '联系人',
          prop: 'contacts',
          group: '乘车人信息',
          width: 70,
        },
        {
          label: '操作',
          prop: 'option',
          group: '系统操作',
          fixed: 'right',
          width: 90,
          customer: true,
        },
      ],
      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['portal']),
    ...mapState([StateTypes.IS_PROJECT_CLOSED]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
    disableMutation() {
      return this[StateTypes.IS_PROJECT_CLOSED];
    },
    popoverWidth() {
      // 如果当前窗口宽度小于 1000，则使用 80%
      if (this.windowWidth < 1100) {
        return this.windowWidth * 0.8;
      }
      // 否则固定为 1000
      return '1100';
    }
  },
  created() {
    this.queryParams.projectId = this.portal.id;
    this.selectedFields = this.tableConfig.map((item) => item.prop)
    this.tableConfig.forEach((item) => {
      if (item.prop === 'useCarMatter') {
        item.enums = this.enums.UseCarMatterEnums
      }
    })
  },
  mounted() {
    this.getData(this.queryParams)
    window.addEventListener('resize', this.onResize);
  },
  beforeDestroy() {
    // 组件销毁时移除监听
    window.removeEventListener('resize', this.onResize);
  },
  methods: {
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    open() {
      this.visible = true
    },
    onCancel() {
      this.detailVisible = false
    },
    handleConfirm() {
      let rows = this.selectedRows
      if (rows && rows.length > 0) {
        let currData = rows[0]
        this.$emit('loadHistory', currData)
        this.visible = false
      } else {
      }
    },
    showDetail(row) {
      this.detailVisible = true
      this.currentData = { bizId: row.id, type: 'view', noAuth: true }
    },
    handleClickRow(row, prop) {
      this.detailVisible = true
      this.currentData = { bizId: row.id, type: 'view', noAuth: true }
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.loading = true
      this.queryParams = { ...this.queryParams, ...query }
      return getCommonFlowList({
        ...this[GetterTypes.GET_GLOBAL_STATE],
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.queryParams,
        columnName: 'tb1.end_time2',
        sort: 'desc',
        userName: this.currUser.userName || this.$storage.get('username'),
        processState: '1',
        searchValue: this.searchValue,
      })
        .then(async (res) => {
          if (res.status) {
            const list = await this.fetchOtherData(res)
            this.$set(this.$data, 'tableData', list)
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    async fetchOtherData(res) {
      const { list, total, isLastPage } = res.data || []
      this.finished = isLastPage
      const carIds = []
      const ids = list.map((it) => {
        carIds.push(it.carId)
        return it.id
      })
      const resFee = await getFeeInfo(ids.join()) // 获取费用信息
      const resCar = await getCarInfo(carIds.join()) // 获取车辆信息
      const resPassenger = await getPassengerList(ids.join()) // 获取乘车人列表
      this.feeInfo = resFee.data
      this.carInfo = resCar.data
      this.passengerInfo = resPassenger.data
      this.total = total
      return list.map((item) => {
        if (this.feeInfo && this.feeInfo[item.id] && this.feeInfo[item.id].length) {
          const { cost1, cost2, cost3, cost4, cost5, cost6, km1, km2, other1, other2 } =
            this.feeInfo[item.id][0]
          item.cost1 = cost1 || 0 // 公路通行费（元）
          item.cost2 = cost2 || 0 // 住宿费（元）
          item.cost3 = cost3 || 0 // 	维修保养费（元）
          item.cost4 = cost4 || 0 // 车辆燃油费
          item.cost5 = cost5 || 0 // 其他费用（元）
          item.cost6 = cost6 || '0.00' // 费用总计（元）
          item.km1 = km1 || 0 // 出车前里程（公里）
          item.km2 = km2 || 0 // 回车后里程（公里）
          item.other1 = other1 || 0 // 出差天数（天）
          item.other2 = other2 || 0 // 加油量（升）
        } else {
          item.cost1 = 0 // 公路通行费（元）
          item.cost2 = 0 // 住宿费（元）
          item.cost3 = 0 // 	维修保养费（元）
          item.cost4 = 0 // 车辆燃油费
          item.cost5 = 0 // 其他费用（元）
          item.cost6 = '0.00' // 费用总计（元）
        }
        if (this.carInfo && this.carInfo[item.carId]) {
          const { carNum, carType } = this.carInfo[item.carId]
          item.carNum = carNum // 车牌号
          item.carType = carType // 车型
        } else {
          item.carNum = '' // 车牌号
          item.carType = '' // 车型
        }
        if (
          this.passengerInfo &&
          this.passengerInfo[item.id] &&
          this.passengerInfo[item.id].length
        ) {
          item.ucPersonFullName = this.getPassengerName(this.passengerInfo[item.id]) // 用车人姓名
          item.ucPersonPhone = this.passengerInfo[item.id][0].ucPersonPhone // 用车人手机号
          item.vdAfUseCarPersonList = this.passengerInfo[item.id]
        } else {
          item.ucPersonFullName = '' // 用车人姓名
          item.ucPersonPhone = '' // 用车人手机号
        }
        item.startTime2=this.$dayjs(item.startTime2).format('YYYY-MM-DD HH:mm')
        item.endTime2=this.$dayjs(item.endTime2).format('YYYY-MM-DD HH:mm')
        return item
      })
    },

    handleRowSelect(selectedRows) {
      let row = selectedRows[selectedRows.length - 1]
      this.selectedRows = [row] // 保存当前选中的行

      // 进入单选模式时，清除所有已选中的行
      const tableRef = this.$refs.historyTableRef.$refs.table;
      tableRef.clearSelection() // 清空所有选中项
      if (selectedRows.length > 0) {
        // 选中当前行
        tableRef.toggleRowSelection(row, true)
      }
    },
    handleSearch() {
      this.searchData(1, this.pageSize, {searchValue: this.searchValue})
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.searchData(1, this.pageSize)
    },
    handleCurrentChange(val) {
      this.searchData(val, this.pageSize)
    },
    getPassengerName(row) {
      return row.map((item) => item.ucPersonFullName).join(',')
    },
  },
}
</script>

<style lang="scss">
</style>
