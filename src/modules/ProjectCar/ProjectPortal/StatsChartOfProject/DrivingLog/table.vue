<template>
  <div>
    <TitleKit>
      <template #left>
        <div style="font-size: 16px; font-weight: 600">行车日志</div>
      </template>
    </TitleKit>
    <TempTable
      ref="table"
      :data-list="tableData"
      :table-config.sync="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      @add-item="handleAdd"
      @delete-item="handleDelete"
      @batch-add="handleBatchAdd"
      @searchData="searchData"
      @clickRow="handleClickRow"
      @export-data="handleExport"
      :showButtons="showButtons"
      :current-page.sync="pageNo"
    >
      <template v-slot:column-useCarNature="{scope}">
        {{ getUseCarNature(scope) }}
      </template>

      <template v-slot:column-operation="{ scope }">
        <div class="flex col-center" style="flex-wrap: nowrap; justify-content: center">
          <div
            style="color: #3c83ff; margin-right: 5px"
            class="cursor-pointer"
            @click.stop="handleDetails(scope)"
          >
            查看
          </div>
        </div>
      </template>
    </TempTable>

    <fks-drawer
      :before-close="beforeClose"
      :visible.sync="visible"
      :wrapperClosable="true"
      :modal="false"
      class="dialog"
      direction="rtl"
      size="600px"
    >
      <header class="card-header drawer-header" slot="title">
        <div class="flex col-center m-b-24">
          <div class="card-title" style="color: #333333">用车详情</div>
          <div class="m-l-20">
            <card-tag class="m-l-10" :tag="{ color: '#3C83FF', text: currentData.projectName }" />
          </div>
        </div>
      </header>
      <TripForm
        v-if="visible"
        v-bind="currentData"
        style="margin: 0; height: 100%; overflow: auto"
      />
    </fks-drawer>
  </div>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'

import { mapGetters, mapState } from 'vuex'
import ProjectForm from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/ProjectForm.vue'
import BatchAddManage from '@modules/ProjectCar/CompanyPortal/ProjectConfig/components/BatchAdd.vue'
import { delProject } from '@modules/ProjectCar/CompanyPortal/ProjectConfig/api'
import {
  exportFlowData,
  getCommonFlowList, getCommonFlowListByPost, getFlowSearchParam
} from '@modules/CarApply/components/api'
import * as StateTypes from '@store/State/stateTypes'
import { getCarInfo, getFeeInfo, getPassengerList } from '@/api/carApply'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import globalSearchMixin from '@/mixins/globalSearchMixin'
import TripForm from '@modules/FormCenter/vehicleDispatch/pc-view.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import { fileReader } from '@utils/exportFile'
import * as GetterTypes from '@store/Getter/getterTypes'
import { ButtonKeys } from '@store/modules/FormController/formFieldConfig'
import { getUserTaskByFormId } from '@modules/Todo/api'
export default {
  name: 'DrivingLog',
  mixins: [globalSearchMixin],
  components: {
    CardTag,
    TripForm,
    TitleKit,
    BatchAddManage,
    ProjectForm,
    TempTable,
  },
  data() {
    return {
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['filter', 'filedConfig', 'export'],
      queryParams: {
        projectId: null,
        conditions: [],
      },
      passengerInfo: null,
      feeInfo: null,
      carInfo: null,
      visible: false,
      tableData: [],
      currentData: {},
      type: 'view',
      selectedFields: [],
      tableConfig: [
        {
          label: '项目名称',
          prop: 'projectName',
          group: '项目信息',
          width: '150px',
        },
        {
          label: '行程编号',
          prop: 'formNum',
          width: '200px',
        },
        {
          label: '用车部门',
          prop: 'task0Department',
          group: '部门信息',
          width: '200px',
        },
        {
          label: '用车性质',
          prop: 'useCarNature',
          group: '用车信息',
          width: '180px',
          customer: true
        },
        {
          label: '用车事由',
          prop: 'useCarMatter',
          group: '用车信息',
        },
        {
          label: '用车事由说明',
          prop: 'remark1',
          group: '用车信息',
          width: '200px',
        },
        {
          label: '车型',
          prop: 'carType',
          group: '车辆信息',
        },
        {
          label: '车牌号',
          prop: 'carNum',
          group: '车辆信息',
          width: '150px',
        },
        {
          label: '乘车人',
          prop: 'ucPersonFullName',
          group: '乘车人信息',
          width: '130px',
        },
        {
          label: '联系电话',
          prop: 'ucPersonPhone',
          group: '乘车人信息',
          width: '150px',
        },
        {
          label: '乘车人数',
          prop: 'useCarPersonNum',
          group: '用车信息',
          width: '100px',
        },
        {
          label: '目的地',
          prop: 'endAddress',
          group: '行程信息',
          width: '200px',
        },
        {
          label: '出发地',
          prop: 'startAddress',
          group: '行程信息',
          width: '200px',
        },
        // {
        //   label: '实际距离',
        //   prop: 'distanceStr',
        //   group: '行程信息',
        // },
        {
          label: '出车前里程',
          prop: 'km1',
          group: '车辆信息',
          width: '130px',
        },
        {
          label: '出车后里程',
          prop: 'km2',
          group: '车辆信息',
          width: '130px',
        },
        {
          label: '公路通行费',
          prop: 'cost1',
          group: '费用信息',
          width: '130px',
        },
        {
          label: '住宿费',
          prop: 'cost2',
          group: '费用信息',
        },
        {
          label: '维修保养费',
          prop: 'cost3',
          group: '费用信息',
          width: '130px',
        },
        {
          label: '车辆燃油费',
          prop: 'cost4',
          group: '费用信息',
        },
        {
          label: '其他费用',
          prop: 'cost5',
          group: '费用信息',
        },
        {
          label: '费用合计',
          prop: 'cost6',
          group: '费用信息',
        },
        {
          label: '出差天数',
          prop: 'other1',
          group: '其他信息',
        },
        {
          label: '加油量',
          prop: 'other2',
          group: '其他信息',
        },
        {
          label: '实际开始时间',
          prop: 'startTime',
          group: '行程信息',
          width: '180px',
        },
        {
          label: '实际结束时间',
          prop: 'endTime',
          group: '行程信息',
          width: '180px',
        },
        {
          label: '操作',
          prop: 'operation',
          fixed: 'right',
          width: '100px',
          customer: true,
        },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
  },
  created() {
    this.queryParams.projectId = this.portal.id
    this.selectedFields = this.tableConfig.map((item) => item.prop)
    this.tableConfig.forEach((item) => {
      if (item.prop === 'useCarMatter') {
        item.enums = this.enums.UseCarMatterEnums
      }
    })
  },
  mounted() {
    getFlowSearchParam().then(res => {
      this.searchConfigs = res.data;
      this.$nextTick(() => {
        // loadDefaultSearchParam里边有通知加载数据的事件
        // this.getData(this.queryParams)
        this.$refs.table.loadDefaultSearchParam();
      })
    })
  },
  methods: {
    getUseCarNature(scope) {
      const enums = JSON.parse(localStorage.getItem('enums'));
      let nature = scope.row.useCarNature
      if (enums && enums.UseCarNatureEnums && nature) {
        let list = enums.UseCarNatureEnums;
        let result = list.find(item => {
          return item.key == nature
        })
        if (result) {
          return result.value;
        }
        return '/'
      }
      return '/';
    },
    async handleDetails(scope) {
      let taskKey = ButtonKeys.VIEW;
      if (scope.row.applyFormState !== 1) {
        const res = await getUserTaskByFormId(scope.row.id);
        if (res.status && res.data && res.data[scope.row.id]) {
          taskKey = res.data[scope.row.id].taskKey;
        }
      }
      this.currentData = {bizId: scope.row.id, type: 'view', noAuth: true, projectName: scope.row.projectName, currentButtonKey: ButtonKeys.VIEW, taskKey: taskKey}
      this.visible = true;
    },
    globalSearch() {
      this.tableData = []
      this.reSearchData()
    },
    reSearchData(refresh) {
      if (!refresh || refresh == false) {
        this.pageNo = 1
      }
      this.getData(this.queryParams)
    },
    async handleDelete(rows) {
      if (rows && rows.length > 0) {
        let currentData = rows[0]
        if (currentData.portalId) {
          let res = await delProject(currentData.portalId)
          if (res.status) {
            this.$message.success('删除成功')
            this.reSearchData(true)
          }
        }
      }
    },
    handleBatchAdd() {
      this.$refs.batchAdd.open()
    },
    handleAdd() {
      this.type = 'plus'
      this.currentData = {}
      this.visible = true
    },
    handleExport(list) {
      let conditions = JSON.parse(JSON.stringify(this.queryParams.conditions))
      conditions.push(
        {
          "sort": null,
          "field": "processState",
          "fieldName": "流程状态",
          "fieldType": "String",
          "fieldEnum": [],
          "operators": [
            {
              "operator": "=",
              "operatorName": "等于"
            }
          ],
          "value": "1",
          "operator": "="
        }
      )
      let data = {
        pageGetParam: {
          ...this.queryParams,
          columnName: 'tb1.end_time2',
          sort: 'desc',
          userName: this.currUser.userName || this.$storage.get('username'),
          processState: '1',
        },
        fields: list
      }
      exportFlowData(data).then(res => {
        try {
          fileReader(res)
        } catch (error) {
          console.error("导出失败:", error);
          alert("导出失败，请稍后重试。");
        }
      })
    },
    handleClickRow(row, prop) {
      this.visible = true
      this.currentData = { ...row }
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.loading = true
      this.queryParams = { ...this.queryParams, ...query }
      return getCommonFlowListByPost({
        ...this[GetterTypes.GET_GLOBAL_STATE],
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        ...this.queryParams,
        columnName: 'tb1.end_time2',
        sort: 'desc',
        userName: this.currUser.userName || this.$storage.get('username'),
        processState: '1',
      }, this[GetterTypes.GET_GLOBAL_STATE])
        .then(async (res) => {
          if (res.status) {
            const list = await this.fetchOtherData(res)
            this.tableData = [...list]
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    async fetchOtherData(res) {
      const { list, total, isLastPage } = res.data || []
      this.finished = isLastPage
      const carIds = []
      const ids = list.map((it) => {
        carIds.push(it.carId)
        return it.id
      })
      const resFee = await getFeeInfo(ids.join()) // 获取费用信息
      const resCar = await getCarInfo(carIds.join()) // 获取车辆信息
      const resPassenger = await getPassengerList(ids.join()) // 获取乘车人列表
      this.feeInfo = resFee.data
      this.carInfo = resCar.data
      this.passengerInfo = resPassenger.data
      this.total = total
      return list.map((item) => {
        if (this.feeInfo && this.feeInfo[item.id] && this.feeInfo[item.id].length) {
          const { cost1, cost2, cost3, cost4, cost5, cost6, km1, km2, other1, other2 } =
            this.feeInfo[item.id][0]
          item.cost1 = cost1 || 0 // 公路通行费（元）
          item.cost2 = cost2 || 0 // 住宿费（元）
          item.cost3 = cost3 || 0 // 	维修保养费（元）
          item.cost4 = cost4 || 0 // 车辆燃油费
          item.cost5 = cost5 || 0 // 其他费用（元）
          item.cost6 = cost6 || '0.00' // 费用总计（元）
          item.km1 = km1 || 0 // 出车前里程（公里）
          item.km2 = km2 || 0 // 回车后里程（公里）
          item.other1 = other1 || 0 // 出差天数（天）
          item.other2 = other2 || 0 // 加油量（升）
        } else {
          item.cost1 = 0 // 公路通行费（元）
          item.cost2 = 0 // 住宿费（元）
          item.cost3 = 0 // 	维修保养费（元）
          item.cost4 = 0 // 车辆燃油费
          item.cost5 = 0 // 其他费用（元）
          item.cost6 = '0.00' // 费用总计（元）
        }
        if (this.carInfo && this.carInfo[item.carId]) {
          const { carNum, carType } = this.carInfo[item.carId]
          item.carNum = carNum // 车牌号
          item.carType = carType // 车型
        } else {
          item.carNum = '' // 车牌号
          item.carType = '' // 车型
        }
        if (
          this.passengerInfo &&
          this.passengerInfo[item.id] &&
          this.passengerInfo[item.id].length
        ) {
          item.ucPersonFullName = this.getPassengerName(this.passengerInfo[item.id]) // 用车人姓名
          item.ucPersonPhone = this.passengerInfo[item.id][0].ucPersonPhone // 用车人手机号
        } else {
          item.ucPersonFullName = '' // 用车人姓名
          item.ucPersonPhone = '' // 用车人手机号
        }
        return item
      })
    },

    getPassengerName(row) {
      return row.map((item) => item.ucPersonFullName).join(',')
    },
    // 提取格式化逻辑，动态根据 tableConfig 配置来格式化
    formatData(data) {
      return data.map((row) => {
        // 处理 carResource
        if (row.carResource) {
          const resourceItem = this.carResource.find((item) => item.key === row.carResource)
          if (resourceItem) {
            row.carResourceStr = resourceItem.value // 设置对应的 label 到 carResourceStr
          }
        }

        // 处理 carStatus
        if (row.carStatus) {
          const statusItem = this.carStatus.find((item) => item.key === row.carStatus)
          if (statusItem) {
            row.carStatusStr = statusItem.value // 设置对应的 label 到 carStatusStr
          }
        }
        return row
      })
    },
    formatDate(row, column) {
      if (!value) return '' // 如果值为空，返回空字符串
      const date = new Date(value)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}` // 返回格式化后的日期字符串 "YYYY-MM-DD"
    },

    onDrawerOpen() {},
    beforeClose(done) {
      this.visible = false
      done()
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
  },
}
</script>

<style scoped lang="less">
@import '~@/styles/headers';
</style>
