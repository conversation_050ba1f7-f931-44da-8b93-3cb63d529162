<template>
  <div>
    <TitleKit>
      <template #left>
        <div class="header-left">
          <div style="font-size: 16px; font-weight: 600">已办列表</div>
          <fks-divider direction="vertical"/>

          <div class="tabs" style="font-size: 14px">
            <div
              class="tab-item"
              :class="{ active: activeTab === 'myStart' }"
              @click="switchTab('myStart')"
            >
              我的发起
            </div>
            <div
              class="tab-item"
              :class="{ active: activeTab === 'othersStart' }"
              @click="switchTab('othersStart')"
            >
              他人发起
            </div>
          </div>
        </div>
      </template>
    </TitleKit>
    <TempTable
      :data-list="tableData"
      :table-config="tableConfig"
      :search-configs="searchConfigs"
      :selected-fields.sync="selectedFields"
      :total="total"
      :loading="loading"
      :current-page.sync="pageNo"
      :showButtons="showButtons"
      :show-selection="false"
      @searchData="searchData"
    >
      <template v-slot:column-useCarMatter="{ scope }">
        {{ getUseCarMatter(scope) }}
      </template>
      <template v-slot:column-startTime="{scope}">
        {{ getStartTime(scope) }}
      </template>
      <template v-slot:column-applyFormState="{ scope }">
        <card-tag style="padding-top: 0;padding-bottom: 0" :tag="{'text': getTagText(scope.row, 'applyFormUserState'), 'color': getTagColor(scope.row)}"/>
      </template>

      <template v-slot:column-operation="{ scope }">
        <div class="flex col-center" style="flex-wrap: nowrap;">
          <div
            style="color: #3c83ff; margin-right: 5px"
            class="cursor-pointer"
            @click.stop="handleDetails(scope)"
          >
            查看
          </div>
          <div
            v-for="btn in scope.row.buttonBig"
            :key="btn.buttonKey"
            style="color: #3c83ff; margin-right: 5px"
            class="cursor-pointer"
            @click.stop="handleProcess(scope, 'execute')"
          >
            {{ btn.buttonValue}}
          </div>
          <fks-dropdown
            v-if="scope.row.buttonsSmall.length > 0"
            @command="handleSmallButtonClick"
          >
            <span style="color: #3c83ff; margin-right: 5px" class="cursor-pointer">更多</span>
            <fks-dropdown-menu slot="dropdown">
              <fks-dropdown-item
                v-for="btn in scope.row.buttonsSmall"
                :key="btn.buttonKey"
                :command="{...scope.row, buttonKey: btn.buttonKey}"
              >
                <span :style="{color: getTextColor(btn.buttonKey)}">{{ btn.buttonValue}}</span>
              </fks-dropdown-item>
            </fks-dropdown-menu>
          </fks-dropdown>
        </div>
      </template>
    </TempTable>

    <fks-drawer
      :before-close="beforeClose"
      :visible.sync="visible"
      :wrapperClosable="true"
      :modal="false"
      class="dialog"
      direction="rtl"
      size="760px"
      @close="handleClose"
    >
      <header class="card-header drawer-header" slot="title">
        <div class="flex col-center m-b-24">
          <div class="card-title" style="color: #333333">用车详情</div>
          <div class="m-l-20">
            <card-tag v-if="currentData" class="m-l-10" :tag="{ color: '#3C83FF', text: currentData.projectName }" />
          </div>
        </div>
      </header>
      <card-view
        v-if="visible && currentData && currentData.type === 'execute'"
        :params="currentData"
        class="full-height"
        @revocation="revocation"
        @refresh="handleRefresh"
        @modify="handleModify"
      />
      <TripForm
        v-if="visible && currentData && currentData.type === 'view'"
        v-bind="currentData"
        ref="tripFormRef"
        style="margin: 0; height: 100%; overflow: auto"
        @init-success="handleInitSuccess"
      />
    </fks-drawer>
    <revocation-drawer
      ref="revocationDrawer"
      :cur-car.sync="curCar"
      :is-cur="true"
      @refreshList="handleRefresh"
    />
    <notification-confirm ref="notificationConfirmRef" />
  </div>
</template>

<script>
import TempTable from '@components/CustomerTable/CustomerTable.vue'
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex'
import * as StateTypes from '@store/State/stateTypes'
import {getCarInfo, getDriverInfo, getFlowButton1} from '@/api/carApply'
import statusTagMixin from '@/mixins/statusTagMixin'
import { getRateContent, getRateList } from '@modules/ProjectCar/ProjectPortal/CarRecord/api'
import storage from '@utils/storage'
import { completedTaskParams, completedTasks, getUserTaskByFormId } from '@modules/Todo/api'
import TitleKit from '@components/PageTitle/TitleKit.vue'
import globalSearchMixin from '@/mixins/globalSearchMixin'
import isConfirmXCMixin from '@/mixins/isConfirmXCMixin'
import TripForm from '@modules/FormCenter/vehicleDispatch/pc-view.vue'
import CardTag from '@components/CardFlow/components/tag.vue'
import ApproveButtons from "@modules/FormCenter/components/ApproveButtons/index.vue";
import CardView from "@modules/FormCenter/card-view.vue";
import RevocationDrawer from "@modules/CarApply/components/RevocationDrawer.vue";
import NotificationConfirm from '@components/NotificationConfirm/pc-view.vue'
import {sleep} from "@utils/util";
import * as GetterTypes from '@store/Getter/getterTypes'
import { changeTips } from '@utils/constants'
import { ButtonKeys, TaskKeys } from '@store/modules/FormController/formFieldConfig'
import * as MutationTypes from '@store/Mutation/mutationTypes'
import * as ActionTypes from '@store/Action/actionTypes'
import EventBus from '@utils/eventBus'

export default {
  name: 'Done',
  mixins: [statusTagMixin, globalSearchMixin, isConfirmXCMixin],
  components: {RevocationDrawer, CardView, ApproveButtons, CardTag, TripForm, TitleKit, TempTable, NotificationConfirm },
  data() {
    return {
      curCar: null,
      suggestions: {},
      loading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      showButtons: ['filter'],
      queryParams: {
        projectId: null,
        conditions: [],
        sponsor: 1
      },
      passengerInfo: null,
      visible: false,
      tableData: [],
      currentData: {},
      type: 'view',
      activeTab: 'myStart',
      selectedFields: [
        'a',
        'projectName',
        'useCarMatter',
        'startAddress',
        'endAddress',
        'startTime',
        'carNum',
        'driverFullName',
        'applyFormState',
        'operation',
      ],
      tableConfig: [
        // {
        //   label: '用车类型',
        //   prop: 'a',
        // },
        {
          label: '项目名称',
          prop: 'projectName',
          width: '150px',
        },
        {
          label: '行程编号',
          prop: 'formNum',
          width: '200px',
        },
        {
          label: '用车事由',
          prop: 'useCarMatter',
          customer: true,
        },
        {
          label: '出发地',
          prop: 'startAddress',
          width: '200px',
        },
        {
          label: '目的地',
          prop: 'endAddress',
          width: '200px',
        },
        {
          label: '用车时间',
          prop: 'startTime',
          width: '150px',
          customer: true
        },
        {
          label: '车牌号',
          prop: 'carNum',
          width: '150px',
        },
        {
          label: '司机',
          prop: 'driverFullName',
        },
        {
          label: '流程状态',
          prop: 'applyFormState',
          group: '行程信息',
          width: '120px',
          customer: true,
        },
        {
          label: '操作',
          prop: 'operation',
          fixed: 'right',
          width: '120px',
          customer: true,
        },
      ],

      // 表格配置（包括字段、搜索配置）
      searchConfigs: [],
      formScroll: false
    }
  },
  computed: {
    ...mapState('CarApply', ['enums', 'currUser']),
    ...mapState(['portal']),
    ...mapState([StateTypes.GLOBAL_QUERY_PARAMS]),
    ...mapGetters([GetterTypes.GET_GLOBAL_STATE]),
  },
  async created() {
    const res = await getRateContent()
    if (res.status) {
      this.suggestions = res.data.reduce((acc, item) => {
        acc[item.score] = item.contents.split('、')
        return acc
      }, {})
    }
    completedTaskParams().then((res) => {
      this.searchConfigs = res.data
    })
    EventBus.$on('openForm', (payload) => {
      this.formScroll = true;
      const newCurrentData = JSON.parse(JSON.stringify(this.currentData));
      this.currentData = null;
      this.$nextTick(()=>{
        this.currentData = {...newCurrentData, ...payload};
      })
    })
  },
  mounted() {
    this.getData(this.queryParams)
  },
  methods: {
    ...mapMutations([MutationTypes.SET_PROJECT_INFO]),
    ...mapActions([ActionTypes.UPDATE_PROJECT_STATUS]),
    handleClose() {
      // formScroll 为true，说明这个弹窗是调度员审批通过后查看短信通知的。之前系统的刷新功能已失效，所以在这里判断一下，并手动调用刷新方法
      if (this.formScroll) {
        this.handleRefresh();
      }
      this.formScroll = false
    },
    handleInitSuccess() {
      // 默认滚动到最底部
      this.formScroll && this.$nextTick(() => {
        const el = this.$refs.tripFormRef.$refs['section-9'].$el;
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
        }
      })
    },
    getTextColor(buttonKey) {
      switch (buttonKey) {
        case 'REVOCATION':
          return '#FF3F4C'
        default:
          return '#3C83FF'
      }
    },
    onSmallButtonClick(row) {
      if (row.buttonKey && changeTips[row.buttonKey]) {
        this.$confirm(changeTips[row.buttonKey], '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.handleSmallButtonClick(row);
        });
      } else {
        this.handleSmallButtonClick(row);
      }
    },
    async handleSmallButtonClick(row) {
      const { projectName, projectId } = row
      await this[ActionTypes.UPDATE_PROJECT_STATUS]({projectName, projectId})
      this[MutationTypes.SET_PROJECT_INFO]({ projectName, projectId })
      const res = await getUserTaskByFormId(row.id);
      if (res.status) {
        const data = res.data ? res.data[row.id] : '';
        const params = {
          type: 'execute',
          formKey: data ? data.formKey : '',
          taskKey: data ? data.taskKey : TaskKeys.VIEW,
          taskId: data ? data.taskId : '',
          bizId: data ? data.formBizId : row.id,
          processInstanceId: data ? data.processInstanceId : '',
          formName: data ? data.formName : '',
          projectName: row.projectName,
          projectId: row.projectId,
          buttonsSmall: row.buttonsSmall
        }
        this.currentData = {...row,...params}

        if (row.buttonKey === "REVOCATION") {
          this.revocation();
        } else {
          this.visible = true;
          // 是否弹出提示
          if (changeTips[row.buttonKey]) {
            const res = await this.getNotification(row.buttonKey);
            if(res === false) {
              // 弹出提示
              this.$refs.notificationConfirmRef.open({buttonKey: row.buttonKey, checked: res});
            }
          }
        }
      }

    },
    handleModify(modifyKey) {
      const newCurrentData = JSON.parse(JSON.stringify(this.currentData));
      this.currentData = null;
      this.$nextTick(()=>{
        const buttonObj = modifyKey ? {buttonKey: modifyKey} : {};
        const {buttonKey, ...rest} = newCurrentData;
        this.currentData = {...rest, ...buttonObj};
      })
    },
    async handleRefresh() {
      this.visible = false;
      await sleep(1000)
      this.reSearchData(true);
    },
    revocation() {
      const {bizId, ...rest} = this.currentData;
      this.curCar = {...rest, id: bizId};
      this.$nextTick(() => {
        this.$refs.revocationDrawer.open();
      })
    },
    switchTab(tab) {
      this.activeTab = tab;
      let sponsorMap = {
        'myStart': 1,
        'othersStart': 2
      }
      this.queryParams.sponsor = sponsorMap[tab];
      this.reSearchData()
    },
    async handleProcess(scope, type) {
      const { projectName, projectId } = scope.row;
      await this[ActionTypes.UPDATE_PROJECT_STATUS]({projectName, projectId})
      this[MutationTypes.SET_PROJECT_INFO]({ projectName, projectId })
      const res = await getUserTaskByFormId(scope.row.id);
      if (res.status) {
        const row = res.data[scope.row.id];
        const params = {
          type: type,
          formKey: row.formKey,
          taskKey: row.taskKey,
          taskId: row.taskId,
          bizId: row.formBizId,
          processInstanceId: row.processInstanceId,
          formName: row.formName,
          projectName: scope.row.projectName,
          projectId: scope.row.projectId,
          buttonsSmall: scope.row.buttonsSmall
        }
        this.currentData = {...row,...params}
        this.visible = true;
      }
      // this.currentData = {bizId: scope.row.id, type: 'execute', projectName: scope.row.projectName}
    },
    async handleDetails(scope) {
      let taskKey = ButtonKeys.VIEW;
      if (scope.row.applyFormState !== 1) {
        const res = await getUserTaskByFormId(scope.row.id);
        if (res.status && res.data && res.data[scope.row.id]) {
          taskKey = res.data[scope.row.id].taskKey;
        }
      }
      this.currentData = {bizId: scope.row.id, type: 'view', noAuth: true, projectName: scope.row.projectName, currentButtonKey: ButtonKeys.VIEW, taskKey: taskKey}
      this.visible = true;
    },
    globalSearch() {
      this.reSearchData()
    },
    getStartTime(scope) {
      return scope.row.startTime ? this.$dayjs(scope.row.startTime).format('YYYY-MM-DD HH:mm') : '/';
    },
    getUseCarMatter(scope) {
      const enums = JSON.parse(localStorage.getItem('enums'))
      if (enums && enums.UseCarMatterEnums) {
        // 查找对应的 value
        const match = enums.UseCarMatterEnums.find(
          (item) => item.key == scope.row.useCarMatter
        );

        // 如果找到匹配项，返回其 value；否则返回空字符串
        return match ? match.value : '';
      }
      return ''
    },
    reSearchData(refresh) {
      if (!refresh || refresh == false) {
        this.pageNo = 1
      }
      this.getData(this.queryParams)
    },
    searchData(pageNo, pageSize, queryParams) {
      this.pageNo = pageNo
      this.pageSize = pageSize
      this.getData(queryParams)
    },
    async getData(query) {
      this.queryParams = { ...this.queryParams, ...query }
      this.loading = true
      return completedTasks(
        {
          ...this.queryParams,
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          columnName: 'createDate',
          sort: 'desc',
        },
        this[GetterTypes.GET_GLOBAL_STATE]
      )
        .then(async (res) => {
          if (res.status) {
            const list = await this.fetchOtherData(res)
            this.tableData = [...list]
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    async fetchOtherData(res) {
      const { list, total, isLastPage } = res.data || []
      this.finished = isLastPage
      const carIds = res.data.list
        .filter((item) => item.carId)
        .map((item) => item.carId)
        .join(',')
      const driverIds = res.data.list
        .filter((item) => item.driverId)
        .map((item) => item.driverId)
        .join(',')
      const rateIds = res.data.list
        .filter((item) => item.id)
        .map((item) => item.id)
        .join(',')
      const { data: carRes } = await getCarInfo(carIds)
      const { data: driverRes } = await getDriverInfo(driverIds)
      const { data: rateRes } = await getRateList({
        ids: rateIds,
        userName: storage.get('username'),
      })
      const {data: buttonRes} = await getFlowButton1(rateIds)

      this.total = total
      return list.map((item) => {
        if (carRes && carRes[item.carId]) {
          const { carNum, id } = carRes[item.carId]
          item.carNum = carNum // 车牌号
          item.vdCarInfoId = id
        } else {
          item.carNum = '' // 车牌号
        }

        if (driverRes && driverRes[item.driverId]) {
          const { driverFullName, id } = driverRes[item.driverId]
          item.driverFullName = driverFullName // 司机名称
          item.vdDriverInfoId = id
        } else {
          item.driverFullName = '' // 司机名称
        }

        if (rateRes && rateRes[item.id]) {
          item.hasRated = true
        } else {
          item.hasRated = false
        }

        if (buttonRes && buttonRes[item.id]) {
          item.buttonsSmall = buttonRes[item.id].filter(button => button.buttonSizeType ==='small' && button.buttonKey !== 'XC_PJ')
          item.buttonBig = buttonRes[item.id].filter(button => button.buttonSizeType ==='big')
        } else {
          item.buttonsSmall = []
          item.buttonBig = []
        }

        // popover visible
        item.popoverVisible = false
        return item
      })
    },
    formatDate(row, column) {
      if (!value) return '' // 如果值为空，返回空字符串
      const date = new Date(value)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}` // 返回格式化后的日期字符串 "YYYY-MM-DD"
    },
    beforeClose(done) {
      this.visible = false
      done()
    },
    clear() {
      this.visible = false
      this.currentData = {}
      this.pageNo = 1
      this.getData(this.queryParams)
    },
  },
}
</script>

<style scoped lang="less">
@import '~@/styles/headers';
.header-left {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: nowrap;

  .fks-divider--vertical {
    height: 2em;
  }
}

.tabs {
  display: flex;
  gap: 30px;
}

.tab-item {
  position: relative;
  color: #333;
  cursor: pointer;
  padding: 8px;
}

.tab-item.active {
  color: #007aff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 50%;
  transform: translateX(-50%);
  width: 85%;
  height: 5px;
  background-color: #007aff;
}

</style>
