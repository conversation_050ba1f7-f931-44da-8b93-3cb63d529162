// 全局样式
@import '~fawkes-mobile-lib/lib/index.css';
@import 'app';
@font-face {
  font-family: 'Source Han Sans SC';
  src: url('../assets/fonts/SourceHanSansSC-Medium-2.otf');
  font-weight: 500;
  font-style: normal;
}
@font-face {
  font-family: 'Source Han Sans SC';
  src: url('../assets/fonts/SourceHanSansSC-Normal-2.otf');
  font-weight: 400;
  font-style: normal;
}

span {
  //  font-size: 28px;
  font-family: Source <PERSON> Sans SC;
}
* {
  font-family: Source Han Sans SC;
}
/* 导航栏样式统一设置 */
.fm-nav-bar__title {
  color: #191919 !important;
  font-size: 36px !important;
  font-weight: 600 !important;
}
.fm-nav-bar .fm-icon {
  color: #000000 !important;
  font-weight: 600 !important;
}
.fm-nav-bar__arrow {
  font-size: 36px !important;
}
.fm-search .fm-icon {
  margin-right: 14px;
}

// 自定义toast样式，目前仅使用于子表组件字段校验失败警告弹窗
.cus-toast {
  z-index: 9999 !important;
}

.fks-input.is-disabled .fks-input__inner,
.fks-select .fks-input.is-disabled .fks-input__inner {
  //background-color: #fff;
  color: #333;
}

.fks-drawer__body {
  padding: 20px 40px 20px 40px;
  overflow-y: auto;
}

.fks-drawer__header {
  margin-bottom: 0;
}
