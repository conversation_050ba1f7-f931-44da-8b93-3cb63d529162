<!--
 * @Author: xie_sm
 * @Date: 2022-02-28 16:07:02
 * @LastEditors: xie_sm
 * @LastEditTime: 2023-03-14 16:30:04
 * @FilePath: \mobile-template\src\App.vue
 * @Description:
 *
-->
<template>
  <div
    v-cloak
    id="app"
    ref="app"
    :class="{[platformStr === 'mobile' ? 'isMobile' : 'isPC']: true}"
    :style="{overflow: inNoAuthPage ? 'auto' : 'hidden'}"
  >
    <fm-skeleton v-if="showSkeleton" :row="5" title/>
    <transition name="fade">
      <router-view key="isNotAlive" class="router"></router-view>
    </transition>
  </div>
</template>
<script>
import {Skeleton, Toast} from 'fawkes-mobile-lib'
import {clickOther, getScrollHeight, getScrollTop, getWindowHeight, scroll} from '@/utils/monitor'
import * as MutationTypes from '@/store/Mutation/mutationTypes'
import {mapActions, mapGetters, mapMutations, mapState} from 'vuex'
import {getRequest, wechatOfficialAccountInIt} from '@/utils/wxapi.js'
import * as StateTypes from '@/store/State/stateTypes'
import feishuLogin from "@/mixins/feishuLogin";
import {getFeishuUserToken} from "@utils/feishuapi";
import platform from "@/mixins/platform";
import HomeHeader from "@components/HomeHeader/index.vue";
import storage from "@utils/storage";
import defaultSettings from "@/config/app/general";

export default {
  components: {
    HomeHeader,
    [Toast.name]: Toast,
    [Skeleton.name]: Skeleton
  },
  mixins: [feishuLogin, platform],
  data() {
    return {
      statusBarHeight: null,
      needUpdate: true,
      platformStr: 'mobile',
    }
  },
  computed: {
    ...mapGetters(['isLogin']),
    ...mapState([StateTypes.APPLY_RESOURCE, StateTypes.LOADED, StateTypes.ROUTES, StateTypes.IS_APP_CREATED, StateTypes.COLLAPSE]),
    ...mapState('CarApply', ['currUser']),
    inNoAuthPage() {
      return this.$route.path.includes('/projectCar/details')
    },
    showSkeleton() {
      return this.$route.path === '/'
    }
  },
  async created() {
    const hasToken = storage.get('access_token');
    if (this[StateTypes.IS_APP_CREATED] && hasToken) {
      return false;
    }
    this.SET_APP_CREATED(true)
    this.onLoad()
    const _this = this
    let ua = navigator.userAgent.toLowerCase();
    const { reLogin } = this.$route.query;
    // 如果微信端链接没有openId,那么说明用户没有从微信公众号进入，所以不会走入下面的逻辑
    if (ua.includes('micromessenger') && window.location.href.includes('openId')) {
      this.layoutPC();
      console.log('公众号')
      _this.SET_APPLY_RESOURCE(2)
      // 如果是从飞书端刚登录后，需要从url里拿到授权码
      const response = await _this.getFeishuAuth();
      if (response && !response.data) {
        Toast(response.message)
        return false
      }
      if (getRequest().code === this.$storage.get('wxCode')) {

      } else {
        wechatOfficialAccountInIt()
      }
    } else if (window.__Lark_Bridge__ || window.lark) {
      console.log('in feishu')
      this.SET_APPLY_RESOURCE(1)
      this.layoutPC();
      // 获取飞书code
      // const code = await getFeishuCode();
      // console.info('🚀🚀', 'code -->', code, `<-- App.vue/created`)
      console.log('is loaded: ', this[StateTypes.LOADED]);
      console.info('🚀🚀', 'hasToken -->', hasToken, `<-- App.vue/created`);
      (!this[StateTypes.LOADED] || reLogin) && this.feishuInit()
    } else {     //都不在
      this.SET_APPLY_RESOURCE(3)
      this.layoutPC();
      await this.getFeishuAuth();
    }
  },
  mounted() {
    window.addEventListener('click', clickOther, true)
    window.addEventListener('scroll', this.triggerScrol, true)
    window.addEventListener('resize', () => {
      this.layoutPC()
    })
  },
  beforeDestroy() {
    window.removeEventListener('storage', function () {
    })
  },
  methods: {
    ...mapActions('WeChat', [
      'getWxCode'
    ]),
    ...mapMutations([MutationTypes.SET_APPLY_RESOURCE, MutationTypes.SET_PLATFORM, MutationTypes.SET_APP_CREATED]),
    handleClick() {
      this[MutationTypes.SET_COLLAPSE](!this[StateTypes.COLLAPSE])
      const val = (this[StateTypes.COLLAPSE]).toString()
      storage.set(StateTypes.COLLAPSE, val);
    },
    async getFeishuAuth() {
      const urlObj = getRequest();
      if (urlObj.code && !urlObj.state) {
        // 手动调用一次飞书登录
        const r = await getFeishuUserToken(urlObj.code);
        // 登录失败！请重试！
        // if (!r.status && r.message === '登录失败！请重试！') {
        //   this.handleLogin();
        // }
        console.info('🚀🚀', 'r -->', r, `<-- App.vue/getFeishuAuth`)
        const defaultRoute = this.isPC ? defaultSettings.projectPortalDefaultRoute : defaultSettings.mobileProjectPortalDefaultRoute;
        if (r.status) {
          await this.$router.replace({
            path: defaultRoute,
            query: {
              code: ''
            }
          })
        }
      }
    },
    layoutPC() {
      // const isMobile = 'ontouchstart' in window;
      const isMobile = /Mobi|Android|iPhone/i.test(navigator.userAgent)
      const platform = isMobile ? 'mobile' : 'pc'
      this.platformStr = platform
      this[MutationTypes.SET_PLATFORM](platform)
      if (!isMobile) {
        const html = document.querySelector("html");
        const body = document.querySelector("body");
        html.style.fontSize = '36px'
        html.style.height = '100vh'
        html.style.display = 'flex'
        html.style.justifyContent = 'center'
        html.style.alignItems = 'center'
        html.style.overflow = 'hidden'
        body.style.width = '100%'
        body.style.height = '100vh'
        body.style.transform = 'translate(0)'
      }
    },
    // 页面加载后添加各事件监听
    onLoad() {
      document.addEventListener('deviceready', this.onDeviceReady, false)
    },
    // Cordova加载完毕
    onDeviceReady() {
      if (typeof yuanchu != 'undefined') {
        yuanchu.statusBar.styleDefault()
      }
      //判断是否需要热更新
      document.addEventListener('chcp_nothingToUpdate', this.nothingToUpdate, false)
      //有些项目不需要热更新
      // if (this.needUpdate) {
      //   this.$loading.show({ title: '检查更新中' })
      //   yuanchu.chcp.fetchUpdate(this.fetchUpdateCallback)
      // }
      // main.js中定义$back
      document.addEventListener('backbutton', this.$back, false) // 默认返回上一级路由，其他情况自行处理
      // 推送
      this.JPushInit()
    },
    JPushInit() {
      document.addEventListener(
        'jpush.receiveRegistrationId',
        function (event) {
          console.log(JSON.stringify(event))
          alert('receiveRegistrationId: ' + JSON.stringify(event))
        },
        false
      )
      document.addEventListener('jpush.openNotification', this.onOpenNotification, false)
      document.addEventListener('jpush.receiveNotification', this.onReceiveNotification, false)
      document.addEventListener('jpush.receiveMessage', this.onReceiveMessage, false)

      try {
        window.JPush.init()
        window.JPush.setDebugMode(true)
        window.setTimeout(this.getRegistrationID, 1000)

        if (yuanchu.device.platform != 'Android') {
          window.JPush.setApplicationIconBadgeNumber(0)
        }
      } catch (exception) {
        console.log(exception)
      }
    },
    onOpenNotification(event) {
      try {
        var alertContent
        if (yuanchu.device.platform == 'Android') {
          alertContent = event.alert
        } else {
          alertContent = event.aps.alert
        }

        badgeNumb = badgeNumb - 1
        badgeNumb = badgeNumb <= 0 ? 0 : badgeNumb
        window.JPush.setBadgeNumber(badgeNumb)

        alert('用户点击消息通知: ' + alertContent)
      } catch (exception) {
        console.log('JPushPlugin:onOpenNotification' + exception)
      }
    },
    onReceiveNotification(event) {
      try {
        var alertContent
        if (yuanchu.device.platform == 'Android') {
          alertContent = event.alert
        } else {
          alertContent = event.aps.alert
        }

        console.log('notificationResult： ' + alertContent)
        alert('接受到消息通知: ' + alertContent)

        badgeNumb = badgeNumb + 1
        window.JPush.setBadgeNumber(badgeNumb)
      } catch (exception) {
        console.log(exception)
      }
    },
    onReceiveMessage(event) {
      try {
        var message
        if (yuanchu.device.platform == 'Android') {
          message = event.message
        } else {
          message = event.content
        }

        console.log('messageResult： ' + message)
        alert('接受到消息: ' + message)

        badgeNumb = badgeNumb + 1
        window.JPush.setBadgeNumber(badgeNumb)
      } catch (exception) {
        console.log('JPushPlugin:onReceiveMessage-->' + exception)
      }
    },
    getRegistrationID() {
      window.JPush.getRegistrationID(this.onGetRegistrationID)
    },
    onGetRegistrationID(data) {
      try {
        console.log('JPushPlugin:registrationID is ' + data)

        if (data.length == 0) {
          var t1 = window.setTimeout(getRegistrationID, 1000)
        }

        console.log('registrationID: ' + data)
        alert('registrationID: ' + data)
      } catch (exception) {
        console.log(exception)
      }
    },
    //无热更新
    nothingToUpdate() {
      this.needUpdate = false
    },
    fetchUpdateCallback(error, data) {
      if (error) {
        //没更新
        this.$loading.hide()
        console.log('data', JSON.stringify(data))
        if (error.code == 2) {
        } else if (error.code == -2) {
          Toast('您的App版本过低，请升级')
          //您的App原生框架版本过低，请升级
        } else {
          //根据error.code对获取更新结果做提示用户、重新调用更新检查方法等其他额外处理
        }
      } else {
        //检查更新正常，开始安装更新
        yuanchu.chcp.installUpdate(this.installationCallback)
      }
    },
    installationCallback(error) {
      if (error) {
        this.$loading.hide()
        //根据error.code对安装更新异常做提示用户、重新调用安装更新方法等其他额外处理
      } else {
        Toast('热更新完成')
        this.$loading.hide()
      }
    },
    triggerScrol() {
      if (Math.round(getScrollTop() + getWindowHeight()) == getScrollHeight()) {
        scroll()
      }
    },
  },
}
</script>
<style lang="less">
//@import "styles/app";
@import "~@assets/css/exclude/font.css";
/* 设置滚动条的样式 */
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}

.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.router {
  height: 100%;
}

#app {
  font-size: 16px;

  &.isMobile {
    height: calc(100vh - constant(safe-area-inset-bottom));
    height: calc(100vh - env(safe-area-inset-bottom));
    //ios底部适配

    padding-bottom: constant(safe-area-inset-bottom); //兼容 IOS<11.2

    padding-bottom: env(safe-area-inset-bottom); //兼容 IOS>11.2
  }

  &.isPC {
    height: 100%;
  }

  background-color: #fff;
  width: 100%;

}

/deep/ .fm-nav-bar {
  padding-top: var(--height);
}

/deep/ .fm-list__finished-text {
  padding-bottom: var(--height);
}

/deep/ .fm-tab--active {
  color: #4545d1;
}

/deep/ .fm-tabs__line {
  background-color: #4545d1;
}

/deep/ .fm-sidebar-item--select::before {
  background-color: #4545d1;
}

</style>
