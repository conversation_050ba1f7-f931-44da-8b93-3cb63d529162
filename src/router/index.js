/*
 * @Author: <EMAIL>
 * @Date: 2019-11-01 11:28:49
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-10-12 11:34:20
 * @Description: 路由根配置
 */
import Vue from 'vue'
import Router from 'vue-router'
import { addAlias } from '@/utils/route'

Vue.use(Router)

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * title: 'title'                 the name show in sidebar and breadcrumb (recommend set)
 * icon: 'svg-name'               the icon show in the sidebar
 * redirect
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
 *  type:                         route/iframe/link
 *  activeMenu: '/Example/list'   the navigation will highlight the path you set
  *  query
  *  params
  *  config: [
  * 'invisible',                  will not in the navigation
  * 'untag',                      will not in the tag bar
  * 'fullScreen',                 page will fill the whole screen; only used to the first level menu
  * 'merge',                      name will be merged in only child case
  * 'noCache',                    the page will no be cached(default is false) used in keep-alive
  * 'affix',                      the tag will affix in the tags-view
  * 'token']                      the page parameter will carry 'fawkes-token'
  }
 */

/**
 * @description: 自定义路由,可自行修改
 *               任何情况下都会加载
 */
export const customRoutes = [

]

/**
 * @description: 仅在 真实作为子应用 运行时加载  (开启子应用模式，并通过主应用访问该应用时才视作真实子应用)
 *               线上/本地路由状态下都会加载
 */
export const microappRoutes = []

/**
 * @description: 不建议修改
 *               仅在 非真实作为子应用 运行时加载   (开启子应用模式，并通过主应用访问该应用时才视作真实子应用)
 *               线上/本地路由状态下都会加载
 */
export const baseRoutes = [
  {
    path: '/',
    meta: { config: ['invisible', 'untag'] },
    component: () => import('@/App.vue')
  },
  {
    path: '/regis',
    name: 'regis',
    component: () => import('@/modules/Register/index.vue'),
    meta: { config: ['fullScreen'] },
  },
  {
    path: '/distribute',
    meta: { config: ['fullScreen'], keepOrigin: true },
    component: () => import('@/modules/Error/distribute'),
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/modules/Login/index.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/my',
    name: 'My',
    component: () => import('@/modules/PersonalCenter/My/index'),
    meta: {  }

  },
  {
    path: '/sign',
    name: 'Sign',
    component: () => import('@/modules/Sign/index.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/apply/details/:type/:bizId',
    name: 'ApplyDetails',
    component: () => import('@/modules/FormCenter/CarApply/index.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/xcck/:code',
    name: 'detailPage',
    component: () => import('@/modules/ProjectCar/DetailLoading/index.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/projectCar/details',
    name: 'details',
    component: () => import('@/modules/ProjectCar/Details/index.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/feedback',
    name: 'feedback',
    component: () => import('@/modules/Feedback/index.vue'),
    meta: { config: ['fullScreen'] },
  },
  {
    path: '/feedback/record',
    name: 'record',
    component: () => import('@/modules/Feedback/components/record.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/feedback/detail/:id',
    name:'feedbackDetails',
    component: () => import('@/modules/Feedback/components/feedbackDetails.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/updateLog',
    name: 'updateLog',
    component: () => import('@/modules/UpdateLog/index.vue'),
    meta: { config: ['fullScreen'] },
  },
  {
    path: '/updateLog/detail/:id',
    name: 'updateDetails',
    component: () => import('@/modules/UpdateLog/components/detail.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/question',
    name: 'question',
    component: () => import('@/modules/CommonQuestion/index.vue'),
    meta: { config: ['fullScreen'] },
  },
  {
    path: '/question/details/:id',
    name: 'questionDetails',
    component: () => import('@/modules/CommonQuestion/components/commonquestionDetail.vue'),
    meta: { config: ['fullScreen'] }
  },
  {
    path: '/error',
    name: 'error',
    redirect: '/404',
    component: () => import('@/modules/Error/index.vue'),
    meta: { config: ['fullScreen'] },
    children: [
      {
        name: 'emptyPortal',
        path: '/emptyPortal',
        component: () => import('@/modules/Error/Error.vue'),
        props: { type: 'emptyPortal' },
        meta: { config: ['fullScreen'] }
      },
      {
        name: '404',
        path: '/404',
        component: () => import('@/modules/Error/Error.vue'),
        props: { type: 404 },
        meta: { config: ['fullScreen'] }
      },
      {
        name: '403',
        path: '/403',
        component: () => import('@/modules/Error/Error.vue'),
        props: { type: 403 },
        meta: { config: ['fullScreen'] }
      },
      {
        name: '500',
        path: '/500',
        component: () => import('@/modules/Error/Error.vue'),
        props: { type: 500 },
        meta: { config: ['fullScreen'] }
      }
    ]
  }
]

export function createRouter () {
  //作为子应用运行时不加载基础路由
  let newBaseRoutes = Vue.prototype.$InQianKun ? microappRoutes : baseRoutes

  //加载自定义路由
  newBaseRoutes = newBaseRoutes.concat(customRoutes)

  if (Vue.prototype.$subApp) {
    for (let i = 0; i < newBaseRoutes.length; i++) {
      addAlias(newBaseRoutes[i])
    }
  }
  return new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({
      y: 0
    }),
    // scrollBehavior(to, from, savedPosition) {
    //   if (to.hash) {
    //     return {
    //       selector: to.hash
    //     }
    //   } else {
    //     return  {
    //       x: 0,
    //       y: 0
    //     }
    //   }
    // },
    routes: [...newBaseRoutes]
    // base:'app'
  })
}

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter () {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export function setRoutes (routes) {
  resetRouter()

  if (Vue.prototype.$subApp) {
    for (let i = 0; i < routes.length; i++) {
      addAlias(routes[i])
    }
  }
  // routes.forEach(item => {
  //   router.addRoute ? router.addRoute(item) : router.addRoute(item)
  // })
  router.addRoutes(routes)
}

// 重写Push方法
const originalPush = Router.prototype.push
Router.prototype.push = function push (location, onResolve, onReject) {
  let prefix = Vue.prototype.$appBasePath

  if (typeof location === 'string' && !location.includes(prefix)) {
    location = prefix + location
  }

  if (
    typeof location === 'object' &&
    !location.name &&
    !location.path.includes(prefix)
  ) {
    if (!(location.meta?.keepOrigin || location.query?.keepOrigin)) {
      location.path = prefix + location.path
    }
  }

  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject)
  }

  return originalPush.call(this, location).catch((err) => err)
}

//重写Replace方法
const originalReplace = Router.prototype.replace
Router.prototype.replace = function replace (location, onResolve, onReject) {
  let prefix = Vue.prototype.$appBasePath

  if (typeof location === 'string' && !location.includes(prefix)) {
    location = prefix + location
  }

  if (
    typeof location === 'object' &&
    !location.name &&
    !location.path.includes(prefix)
  ) {
    if (!(location.meta?.keepOrigin || location.query?.keepOrigin)) {
      location.path = prefix + location.path
    }
  }

  if (onResolve || onReject) {
    return originalReplace.call(this, location, onResolve, onReject)
  }

  return originalReplace.call(this, location).catch((err) => err)
}

const router = createRouter()

export default router
