/*
 * @Des:
 * @Author: jin_yc
 * @Date: 2020-01-06 15:46:12
 * @LastEditor: jin_yc
 * @LastEditTime: 2022-04-11 14:50:34
 */
import './config/microapp/public-path'
import Vue from 'vue'
import Api from '@/config/api'
import storage from '@/utils/storage'
import { tsDvalue } from './config'
import VueI18n from 'vue-i18n'
import { initAsSubApp, initAsIndependentApp } from '@/microapp/util'
import FawkesLib from 'fawkes-lib';
import 'fawkes-lib/lib/theme-chalk/index.css' //组件库css
import Vue2TouchEvents from 'vue2-touch-events'
import {hasNewVersion} from "./utils/util";
import { LOADING_USER_DATA } from '@/store/Action/actionTypes'

Vue.use(FawkesLib, { preciseSearch: true, hideSwitch: true })
Vue.config.productionTip = false
//在install前注入环境变量
Object.assign(Vue.prototype, Api)

if (!window.__POWERED_BY_QIANKUN__) {
  initAsIndependentApp()
  hasNewVersion().then(() => {
    render()
  }).catch((err) => {
    console.log('渲染应用失败：', err)
  })
}

var instance = null

/**
 * @description: 初始化入口
 *
 */
async function render(props = {}) {
  const { container } = props

  //toDo 为了保证环境变量注入完成，再加载路由
  const { setTs } = require('@/utils/request/sign')
  const store = require('@/store').default
  const install = require('@/utils/hooks/loadPlugins').default
  const router = require('@/router').default
  const App = require('@/App.vue').default

  Vue.use(install)
  Vue.use(Vue2TouchEvents)
  let ua = navigator.userAgent.toLowerCase();
  if(ua.match(/MicroMessenger/i)=="micromessenger") {
    wx.miniProgram.getEnv(async (res)=>{
      if (res.miniprogram) {        //在小程序
      } else {                   //在公众号
        if (storage.get('access_token')) {
          await setTs()
        }
      }
    })
  } else {
    await setTs()
  }
  // await setTs()
  window.setInterval(() => {
    setTs()
  }, tsDvalue)
  await store.dispatch(LOADING_USER_DATA)

  Vue.use(VueI18n)

  const i18n = new VueI18n({
    locale: 'zh', // 设置语言,默认为中文
    messages: {
      zh: require('./assets/lang/zh'), //中文
      en: require('./assets/lang/en'), //英文
    },
  })
  instance = new Vue({
    router,
    store,
    i18n,
    render: h => h(App)
  }).$mount('#app')
}

window.onbeforeunload = function() {
  // console.info('🚀🚀', '页面刷新之前触发 -->', `<-- main.js/onbeforeunload`)
  // 清除本地存储
  // storage.clear();
}

