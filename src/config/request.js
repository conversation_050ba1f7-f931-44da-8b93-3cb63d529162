/**
 *  httpMethod:post、get、delete、*
 *  scope:req(传参不加密，返回加密)、res(传参加密，返回不加密)、*(全不加密)
 */
 export const ignore = [
    {
      url: '/sys-auth/oauth/token',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-gateway/sign/ts',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sign/ts',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-auth/oauth/behaviour_captcha',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-auth/oauth/exit',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-auth/oauth/user_info',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-storage/download',
      httpMethod: 'get',
      scope: 'res'
    },
    {
      url: '/sys-auth/oauth/sms_captcha',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-auth/oauth/img_captcha',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-system/oauth/clients',
      httpMethod: 'get',
      scope: '*'
    },
    {
      url: '/sys-system/oauth/client',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-system/menus/import',
      httpMethod: 'post',
      scope: 'req'
    },
    {
      url: '/sys-system/menus/export',
      httpMethod: '*',
      scope: 'res'
    },
    {
      url: '/sys-system/buttons/import',
      httpMethod: 'post',
      scope: 'req'
    },
    {
      url: '/sys-system/buttons/export',
      httpMethod: '*',
      scope: 'res'
    },
    {
      url: '/sys-system/dictionary/import',
      httpMethod: 'post',
      scope: 'req'
    },
    {
      url: '/sys-system/dictionary/export',
      httpMethod: '*',
      scope: 'res'
    },
    {
      url: '/sys-auth/oauth/check_behaviour_captcha',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-user/users/export',
      httpMethod: '*',
      scope: 'res'
    },
    {
      url: '/sys-user/users/import',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-user/user/template',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-storage/zip',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/tx-sys-user/page',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/tx-sys-system/page',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/tx-sys-user/testUser',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-storage/upload',
      httpMethod: '*',
      scope: 'req'
    },
    {
      url: '/sys-signature/file',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-storage/file',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-signature/license',
      httpMethod: 'post',
      scope: '*'
    },
    {
      url: '/sys-signature/license',
      httpMethod: 'get',
      scope: 'req'
    },
    {
      url: '/sys-system/testSm2',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-system/testSm4',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-system/testAes',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-system/testDes',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-system/testRsa',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-sso/oauth2/login',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-user/oauth/users',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-auth/oauth/user/lock/num',
      httpMethod: '*',
      scope: 'req'
    },
    {
      url: '/system/info',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/licenseInfo',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/license/install',
      httpMethod: '*',
      scope: '*'
    },
    {
      url: '/sys-user/user/register',
      httpMethod: '*',
      scope: 'req'
    },
    {
      url: '/sys-user/user/retrieve/pwd',
      httpMethod: '*',
      scope: 'req'
    },
    {
      url: '/sys-form/**',
      httpMethod: '*',
      scope: 'req'
    },
    {
      url: '/sys-bpm/**',
      httpMethod: '*',
      scope: 'req'
    }
  ]
  //需要加签名访问的接口
  export const sign = [
    '/sys-auth/oauth/token',
    '/sys-auth/oauth/sms_captcha',
    '/sys-auth/oauth/client/exit',
    '/sys-auth/oauth/client/all/exit',
    '/sys-auth/oauth/user/lock/num',
    '/sys-auth/oauth/img_captcha',
    '/sys-auth/oauth/behaviour_captcha',
    '/sys-auth/oauth/check_behaviour_captcha',
    '/sys-user/oauth/user/bind',
    '/sys-user/user/sms_register_captcha',
    '/sys-user/user/register',
    '/sys-user/users/cache/sync',
    '/sys-user/user/sms_captcha',
    '/sys-user/user/retrieve/pwd',
    '/sys-system/clients/cache/sync',
    '/sys-system/dictionary/cache/sync',
    '/sys-system/lang/detail/name',
    '/sys-system/clientInfo',
    '/sys-system/dictionary/detail/list',
    '/sys-sso/oauth2/login',
    '/sys-bpm/process/history',
    '/vehicle-dispatch/vd/auth/login',
    '/vehicle-dispatch/vd/auth/register',
    '/vehicle-dispatch/vd/auth/register/verify',
      '/vehicle-dispatch/vd/auth/feishu/verify',
    '/vehicle-dispatch/vd/auth/register/sms',
    '/vehicle-dispatch/feishu/open-apis/authen/v1/access_token',
    '/vehicle-dispatch/vd/auth/feishu/config',
    '/vehicle-dispatch/vd/auth/wxgzh/config',
    '/vehicle-dispatch/wx/getMsg',
    '/vehicle-dispatch/vd/flow/detail',
    '/vehicle-dispatch/vd/system/dict',
    '/vehicle-dispatch/vd/af/startAddress',
    '/vehicle-dispatch/vd/af/carCompany',
    '/vehicle-dispatch/vd/af/carDispatchUser',
    '/vehicle-dispatch/vd/af/carCompany/approver',
    '/vehicle-dispatch/clear/user',
    '/vehicle-dispatch/vd/flow/uc/car',
    '/vehicle-dispatch/vd/flow/uc/driver',
    '/vehicle-dispatch/vd/flow/uc/fy',
    '/vehicle-dispatch/vd/flow/uc/person',
    '/vehicle-dispatch/tips/query',
    '/sys-storage/file',
    '/vehicle-dispatch/vd/af/sp/user/list',
    '/vehicle-dispatch/vd/xcck/info',
    '/vehicle-dispatch/vd/af/uc/complete/isEvaluate',
    '/vehicle-dispatch/vd/ai/sensitive/key',
    '/vehicle-dispatch/vd/flow/uc/waypoint',
    '/vehicle-dispatch/vd/user/phone/exist',
    '/vehicle-dispatch/vd/user/name/exist',
    '/vehicle-dispatch/vd/user/regis',
    '/sys-user/user/sms_register_captcha',
    '/vehicle-dispatch/vd/user/getDeptByInviteCode',
    '/vehicle-dispatch/vd/xcck/verify/phone'
  ]
  //需要不提示返回信息的接口
  export const cMsg = [
    '/sys-auth/oauth/token',
    '/sys-auth/oauth/user_info',
    '/sys-auth/oauth/img_captcha',
    '/sys-auth/oauth/sms_captcha',
    '/sys-auth/oauth/behaviour_captcha',
    '/sys-auth/oauth/check_behaviour_captcha',
    '/sys-auth/oauth/render_url/*',
    '/sys-user/oauth/user/bind',
    '/sys-user/users/cache/sync',
    '/sys-user/user/portals',
    '/sys-user/user/sms_captcha',
    '/sys-gateway/sign/ts',
    '/sys-system/clients/cache/sync',
    '/sys-sso/oauth2/user_info',
    '/sys-sso/oauth2/login',
    '/sys-signature/license',
    '/sys-signature/file',
    '/sys-monitor/analysis',
    '/system/info',
    '/licenseInfo',
    '/sys-user/user/menus',
    '/vehicle-dispatch/feishu/open-apis/authen/v1/access_token',
    '/vehicle-dispatch/vd/auth/login'
  ]
