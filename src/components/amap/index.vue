<template>
  <div class="position-relative" style="flex-grow: 1">
    <fks-autocomplete
      ref="autocompleteRef"
      v-model="searchVal"
      :fetch-suggestions="remoteMethod"
      :popper-append-to-body="false"
      class="location-selector"
      :placeholder=" isMobile ? mobileNotify : pcNotify"
      popper-class="my-autocomplete"
      trigger-on-focus
      @input="handleInput"
      @select="handleLocationChange"
      @keyup.enter.native="$emit('enter')"
    >
      <template slot-scope="{ item }">
        <div v-if="item.userName && isPC" class="option savedOption">
          <div v-if="isPC" class="flex">
            <i class="fks-icon-star-on" style="color: #f4ea2a" @click.stop="handleDelete(item)"/>
            <div class="places">
              <div class="name">{{ item.address }}</div>
            </div>
          </div>
          <div
            style="cursor: pointer"
            @click.stop="handleDelete(item)"
          >
            <i class="fks-icon-delete"
               style="color: #ff4d4f"/>
          </div>
        </div>
        <div v-if="item.userName && isMobile">
          <fm-swipe-cell class="mobileSavedOption" stop-propagation>
            <div class="flex full-width" style="align-items: baseline">
              <i class="fks-icon-star-on" style="color: #f4ea2a;font-size: 18px" @click.stop="handleDelete(item)"/>
              <div class="mobile-places">
                <div class="name">{{ item.address }}</div>
              </div>
            </div>
            <template #right>
              <fm-button class="delete-button" label="删除" square type="danger"
                         @click="handleDelete(item)"/>
            </template>
          </fm-swipe-cell>
        </div>
        <div v-if="!item.userName" class="option">
          <i class="fks-icon-location-outline"/>
          <div class="places">
            <div class="name" v-html="highlightSearchValue(item.name)"/>
            <div class="address" v-html="highlightSearchValue(item.district + item.address)"/>
          </div>
        </div>
      </template>
      <i
        slot="suffix"
        class="fks-icon-star-off star" style="font-size: 20px;cursor: pointer" @click="saveToDefaultAddress"/>
    </fks-autocomplete>
    <div v-if="isPC" class="map-switcher flex col-center row-bewteen">
      <div
        v-for="(item, index) in mapTypes"
        :key="index"
        class="map-type flex flex-column cursor-pointer row-center"
        style="margin: 0 8px"
        @click="showSatellite = (item.value === 'satellite')"
      >
        <div
          style="height: 40px;width: 60px"
          class="img-frame position-relative"
          :class="{active: showSatellite === (item.value ==='satellite')}"
          :style="{backgroundImage: `url(${require(`@/assets/img/amap/${item.value}-map.png`)})`}"
        >
          <div
            v-if="showSatellite === (item.value === 'satellite')"
            class="checked flex col-center row-center"
          >
            <i class="fks-icon-check" style="color: white" />
          </div>
        </div>
        <div class="map-name m-t-2">{{item.name}}</div>
      </div>
    </div>

    <div id="amap-container" :class="isFull ? 'full-height' : 'height-100'" class="full-width"></div>
  </div>
</template>

<script>
import axios from 'axios';
import {addSavedAddress, deleteSavedAddress, getSavedAddressList} from "@components/amap/api";
import platform from "@/mixins/platform";
import aMapMixin from "@/mixins/aMapMixin";
import OverflowTooltip from "@components/OverflowTooltip/index.vue";
import {Toast} from "fawkes-mobile-lib";

let DEFAULT_COORDINATE = [120.017677, 30.252525];
export default {
  name: 'AMap',
  components: {OverflowTooltip},
  mixins: [platform, aMapMixin],
  props: {
    locationInfo: {
      type: Object
    },
    isFull: {
      type: Boolean,
      default: false
    },
    addressType: {
      type: String,
      default: 'start'
    }
  },
  data() {
    return {
      loading: false,
      query: '',
      options: [],
      searchVal: '',
      map: '',
      marker: '',
      circle: '',
      geoCoder: null,
      savedAddress: [],
      satelliteLayer: null,
      showSatellite: false,
      mapTypes: [
        {name: '标准地图', value: 'standard'},
        {name: '卫星地图', value:'satellite'},
      ],
      mobileNotify: "选中地点或输入后直接确定",
      pcNotify: "请输入地名,若无法搜到,可直接手动定位（若无需精确定位,可手动输入地址后直接点击‘确定’或者按回车键提交）",
    };
  },
  async created() {
    await this.getAddress();
    this.$nextTick(async () => {
      const {longitude, latitude, name} = this.locationInfo;
      // 判断有无传经纬度，如果没有就复制默认的
      longitude && latitude && (DEFAULT_COORDINATE = [longitude, latitude]);
      // 判断有无传地址，如果有就根据地址搜索
      if (name && longitude && latitude) {
        // 加载高德地图插件
        await this.initAMap();
        //根据地址搜索
        this.getLngLat(name, true)
      } else {
        await this.initMap();
      }
    });
  },
  methods: {
    handleDelete(row) {
      deleteSavedAddress(row.id).then(res => {
        if (res.status) {
          this.isPC && this.$message.success('删除成功');
          this.isMobile && Toast.success('删除成功');
          const index = this.savedAddress.findIndex(item => item.id === row.id);
          this.savedAddress.splice(index, 1)
          if (this.$refs.autocompleteRef) {
            this.$refs.autocompleteRef.suggestions.splice(index, 1)
          }
        }
      })
    },
    async getAddress() {
      // 获取历史，收藏地址列表
      const {data: savedAddress} = await getSavedAddressList(2);
      this.savedAddress = savedAddress.reverse();
    },
    saveToDefaultAddress() {
      if (this.locationInfo.name) {
        const userName = this.$storage.getObject('user').userName;
        const data = {
          address: this.locationInfo.name,
          addressX: this.locationInfo.latitude,
          addressY: this.locationInfo.longitude,
          addressType: 2,
          userName
        }
        addSavedAddress(data).then(res => {
          if (res.status) {
            this.isPC && this.$message.success('收藏成功');
            this.isMobile && Toast.success('收藏成功');
            if (this.$refs.autocompleteRef) {
              this.$refs.autocompleteRef.suggestions.unshift(res.data)
            }
            this.getAddress();
          }
        })

      } else {
        this.isPC && this.$message.warning('请输入地址')
        this.isMobile && Toast.warning('请输入地址')
      }
    },
    remoteMethod(query, cb) {
      if (query) {
        this.query = query
        this.loading = true;
        const obj = {
          key: process.env.VUE_APP_AMAP_WEB_KEY,
          keywords: query
        }
        const params = Object.keys(obj).map(key => (`${key}=${obj[key]}`)).join('&')
        const url = `https://restapi.amap.com/v3/assistant/inputtips?${params}`
        fetch(url)
          .then(res => res.json())
          .then(res => {
            if (res.status === '1') {
              const searchOptions = res.tips
                .filter(tip => typeof tip.id === 'string')
                .map(tip => {
                  const newObj = {}
                  const keys = Object.keys(tip)
                  keys.forEach(key => {
                    if (typeof tip[key] === 'string') {
                      newObj[key] = tip[key]
                    } else {
                      newObj[key] = ''
                    }
                  })
                  return newObj;
                })
              cb([...this.savedAddress, ...searchOptions])
            } else {
              cb([])
            }
          })
          .finally(() => this.loading = false)
      } else {
        cb(this.savedAddress)
      }
    },
    handleEnter() {
      console.log('回车了，关闭弹窗')
    },
    handleInput(val) {
      const params = {
        name: val,
        longitude: '',
        latitude: '',
      }
      this.$emit('update:locationInfo', params);
      this.$emit('update', params);
    },
    handleLocationChange(item) {
      if (item) {
        if (item.userName) {
          const params = {
            name: item.address,
            longitude: item.addressY,
            latitude: item.addressX,
            range: this.locationInfo.range || 0
          }
          this.$emit('update:locationInfo', params);
          this.$emit('update', params);
          this.searchVal = item.address;
          this.setLayer(item.addressY, item.addressX);
        } else {
          const {district, name, location} = item;
          let longitude, latitude;
          if (location) {
            [longitude, latitude] = location.split(',')
          }
          const params = {
            name: district + name,
            longitude: longitude,
            latitude: latitude,
            range: this.locationInfo.range || 0
          }
          this.$emit('update:locationInfo', params);
          this.$emit('update', params);
          this.searchVal = district + name;
          if (longitude && latitude) {
            this.setLayer(longitude, latitude);
          }
        }
      }
    },
    highlightSearchValue(str) {
      // 找到搜索值，并高亮
      const index = str.indexOf(this.query)
      if (index > -1) {
        const highlightText = str.substr(index, this.query.length)
        const beforeHighlightText = str.substring(0, index)
        const afterHighlightText = str.substring(index + this.query.length, str.length)
        return `${beforeHighlightText}<span style="color: steelblue">${highlightText}</span>${afterHighlightText}`
      } else {
        return str
      }
    },
    getLngLat(address, isInit = false) {
      const _this = this;
      this.searchVal = address
      AMap.plugin('AMap.Geocoder', function () {
        var geocoder = new AMap.Geocoder();
        geocoder.getLocation(address, function (status, result) {
          if (status === 'complete' && result.info === 'OK') {
            // 经纬度
            const lng = result.geocodes[0].location.lng;
            const lat = result.geocodes[0].location.lat;
            // 拿到的经纬度
            _this.$emit('updateLocationInfo', {
              name: address,
              longitude: lng,
              latitude: lat,
              range: _this.locationInfo.range || 0
            });
            _this.$emit('update', {
              name: address,
              longitude: lng,
              latitude: lat,
              range: _this.locationInfo.range || 1
            });
            if (isInit) {
              DEFAULT_COORDINATE = [lng, lat]
              _this.initMap();
            }
            _this.setLayer(lng, lat)
          } else {
            console.log('定位失败！');
          }
        });
      });
    },
    removeMarker() {
      const map = this.map;
      if (this.marker) {
        map.remove(this.marker);
      }
    },
    setMarker(longitude, latitude) {
      this.removeMarker();
      this.marker = new AMap.Marker();
      this.marker.setMap(this.map);
      this.marker.setPosition([longitude, latitude]);
      this.map.setCenter(this.marker.getPosition());
    },
    setRanger(longitude, latitude) {
      if (this.circle) {
        this.map.remove(this.circle);
      }
      this.circle = new AMap.Circle({
        center: new AMap.LngLat(longitude, latitude), // 圆心位置
        radius: this.locationInfo.range, // 圆半径
        fillColor: '#1791fc', // 圆形填充颜色
        fillOpacity: 0.4,
        strokeOpacity: 0.1,
        strokeColor: '#fff', // 描边颜色
        strokeWeight: 1 // 描边宽度
      });
      this.map.add(this.circle);
    },
    setLayer(longitude, latitude) {
      this.setMarker(longitude, latitude);
      if (this.locationInfo.range) {
        this.setRanger(longitude, latitude);
      }
    },
    async initMap() {
      await this.initAMap();
      this.map = new AMap.Map('amap-container', {
        resizeEnable: true,
        zoom: 15,
        center: DEFAULT_COORDINATE
      });
      // 初始化卫星地图图层
      this.satelliteLayer = new AMap.TileLayer.Satellite();
      this.map.add(this.satelliteLayer);
      // 先暂时不显示卫星图
      this.satelliteLayer.hide();

      this.map.on('click', (evt) => {
        const {lng, lat} = evt.lnglat;
        this.setLayer(lng, lat);
        this.setAddressByLngLat(lng, lat);
      });
      const {longitude, latitude, name} = this.locationInfo;
      if (longitude && latitude) {
        this.setLayer(longitude, latitude);
      } else {
        // todo 暂时禁用web端定位功能，待后续开发
        return;
        try {
          const {status, result} = await this.getDefaultLocation();
          if (status === 'complete') {
            // 如果用户已经填写了地址，就不需要根据地址获取定位了
            if (!name) {
              const lng = result.position.lng;
              const lat = result.position.lat;
              // 没选中地址的场景下，仅出发地默认选中，其余待用户选择
              if (this.addressType === 'start' && !name) {
                this.setAddressByLngLat(lng, lat);
              }
            } else {
              // 如果用户已经填写了地址，自动回填到地址搜索框中
              this.searchVal = name;
            }
          } else if (status === 'error') {
            // this.$message.error(result.message)
            console.info('🚀🚀', '获取定位失败 -->', result.message, `<-- index.vue/initMap`)
          }

        } catch (e) {
          console.info('🚀🚀', 'e -->', e, `<-- index.vue/initMap`)
        }
      }

    },
    getQueryParams(params) {
      return Object.keys(params).map(key => (`${key}=${params[key]}`)).join('&')
    },
    setAddressByLngLat(lng, lat) {
      const params = {
        key: process.env.VUE_APP_AMAP_WEB_KEY,
        location: `${lng},${lat}`,
        radius: 100
      }
      const queryParams = this.getQueryParams(params);
      axios.get(`${process.env.VUE_APP_AMAP_URL}/v3/geocode/regeo?${queryParams}`).then(res => {
        if (res.status === 200) {
          const {regeocode} = res.data;
          const {formatted_address} = regeocode;
          // 如果当前经纬度无法解析出地址，则返回空字符串
          const addressName = Array.isArray(formatted_address) ? formatted_address[0] || '' : formatted_address;
          this.searchVal = formatted_address;
          this.$emit('update:locationInfo', {
            name: addressName,
            longitude: lng,
            latitude: lat,
            range: this.locationInfo.range || 0
          });
          this.$emit('update', {
            name: addressName,
            longitude: lng,
            latitude: lat,
            range: this.locationInfo.range || 0
          });
        } else {
          this.searchVal = '';
          this.$emit('update:locationInfo', {
            name: '',
            longitude: lng,
            latitude: lat,
            range: this.locationInfo.range || 0
          });
          this.$emit('update', {
            name: '',
            longitude: lng,
            latitude: lat,
            range: this.locationInfo.range || 0
          });
        }
      });
    },
    poiPickerReady(poiPicker) {
      // 选取了某个POI
      poiPicker.on('poiPicked', (poiResult) => {
        const {name, location, district} = poiResult.item;
        if (!location) {
          this.getLngLat(district + name);
          return false
        }
        this.$emit('update:locationInfo', {
          name: district + name,
          longitude: location.lng,
          latitude: location.lat,
          range: this.locationInfo.range || 0
        });
        this.$emit('update', {
          name: district + name,
          longitude: location.lng,
          latitude: location.lat,
          range: this.locationInfo.range || 0
        });
        this.searchVal = district + name;
        this.setLayer(location.lng, location.lat);
      });
    },
    getDefaultLocation() {
      return new Promise((resolve, reject) => {
        if (this.map) {
          window.AMap.plugin('AMap.Geolocation', () => {
            const geolocaiton = new window.AMap.Geolocation({
              enableHighAccuracy: true,//是否使用高精度定位，默认:true
              timeout: 10000,          //超过10秒后停止定位，默认：5s
              buttonPosition: '',    //定位按钮的停靠位置
              buttonOffset: new window.AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
              zoomToAccuracy: true,   //定位成功后是否自动调整地图视野到定位点
            })
            this.map.addControl(geolocaiton)
            geolocaiton.getCurrentPosition((status, result) => {
              resolve({status, result})
            }, (err) => {
              reject(err)
            })
          })
        } else {
          reject('地图未初始化')
        }
      })
    }
  },
  watch: {
    showSatellite(newVal) {
      if (this.satelliteLayer) {
        if (newVal) {
          this.satelliteLayer.show();
        } else {
          this.satelliteLayer.hide();
        }
      }
    },
    locationInfo: {
      deep: true,
      handler(newVal) {
        const {longitude, latitude, name} = newVal;
        this.searchVal = name;
        if (longitude && latitude) {
          DEFAULT_COORDINATE = [longitude, latitude];
          this.setLayer(longitude, latitude);
        } else {
          this.removeMarker()
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .amap-logo {
  display: none;
  opacity: 0 !important;
}

/deep/ .amap-copyright {
  opacity: 0;
}

/deep/ .fks-autocomplete-suggestion li {
  padding: 0 20px;
}

/deep/ .fks-input__suffix {
  top: 8px;
}

.address-container {
  display: grid;
  grid-template-rows: 1fr;
  grid-row-gap: 20px;
  width: 40%;
  margin-right: 40px;

  .star:hover {
    color: #3C83FF !important;
  }

  .table {
    margin-top: 20px;
  }

  .address-item {
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}

.mobileSavedOption {
  min-height: 80px;
}

.mobile-places {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
  width: 100%;
  white-space: break-spaces;
  touch-action: manipulation;


  .name {
    font-size: 28px;
    font-weight: bolder;
    vertical-align: top;
  }

  .address {
    font-size: 24px;
    line-height: 24px;
    flex-wrap: wrap;
    padding-bottom: 20px;
  }
}

.location-selector {
  top: 20PX;
  left: 20PX;
  right: 20PX;
  z-index: 9;
  position: absolute;

  .option {
    display: flex;
    align-items: flex-start;

    &.savedOption {
      align-items: center;
      justify-content: space-between;
    }

    i {
      font-size: 32px;
      display: inline-block;
      margin-top: 20px;
    }


    .places {
      display: flex;
      flex-direction: column;
      margin-left: 10px;
      width: calc(100% - 32px);
      white-space: break-spaces;
      touch-action: manipulation;

      .name {
        font-size: 28px;
        font-weight: bolder;
      }

      .address {
        font-size: 24px;
        line-height: 24px;
        flex-wrap: wrap;
        padding-bottom: 20px;
      }
    }
  }
}

.map-switcher {
  position: absolute;
  bottom: 50px;
  right: 50px;
  z-index: 1;
  border-radius: 4px;
  background: #FFFFFF;
  box-sizing: border-box;
  border: 1px solid #DDDDDD;
  padding: 10px 6px;

  .map-type {
    box-sizing: border-box;
    .img-frame {
      box-sizing: border-box;
      &.active {
        border: 5px solid #1A88FF;
      }

      .checked {
        background: #1A88FF;
        bottom: 0;
        left: 0;
        position: absolute;
        right: 0;
        height: 25px;
      }
    }
  }

  .map-name {
    font-size: 24px;
    font-weight: normal;
    line-height: normal;
    text-align: center;
    letter-spacing: 0px;
    color: #333333;
  }
}

</style>
