<template>
  <fks-dialog
    :visible.sync="visible"
    width="90%"
    :show-close="false"
    class="sms-status-dialog-wrapper"
  >
    <div slot="title" class="dialog-header">
      <div class="d-flex align-items-center">
        <span class="dialog-title">短信通知状态</span>
        <span class="dialog-subtitle"
          >（如遇多次重复短信的情况，请手动联系相关人员，并向向系统管理员反馈问题）</span
        >
      </div>
      <div class="dialog-actions">
        <fks-button
          :disabled="type === 'view'"
          type="text"
          size="small"
          @click="handleResendAll"
        >
          <i class="fks-icon-refresh"></i>
          重新发送所有失败通知
        </fks-button>
        <i
          @click="handleClose"
          class="fks-icon-close"
          style="color: #909399; margin-left: 24px; font-size: 14px; cursor: pointer"
        />
      </div>
    </div>
    <fks-table
      :data="smsData"
      style="width: 100%; overflow: auto"
      height="400"
      border
      :header-cell-style="{
        background: '#FAFAFA',
        color: '#333333 !important',
        fontSize: '14px !important',
        fontWeight: 'unset !important',
      }"
      :cell-style="{ color: '#333333 !important' }"
    >
      <fks-table-column type="index" label="#" width="50" align="center" />

      <fks-table-column prop="approvalStep" label="审批环节" width="120" align="center" />

      <fks-table-column prop="notifyRole" label="通知角色" width="100" align="center" />

      <fks-table-column prop="notifyTarget" label="通知对象" width="80" align="center" />

      <fks-table-column prop="phone" label="手机号" width="130" align="center" />

      <fks-table-column prop="content" label="短信内容" min-width="300">
        <template slot-scope="{ row }">
          <div class="sms-content">
            {{ row.content }}
          </div>
        </template>
      </fks-table-column>

      <fks-table-column prop="status" label="发送状态" width="140" align="center">
        <template slot-scope="{ row }">
          <div class="status-cell">
            <div class="status-text">
              <img
                :src="require('@/assets/img/sms/send-success.svg')"
                width="14px"
                height="14px"
                alt="#"
              />
              <span class="m-l-10 success">发送成功</span>
            </div>
            <div class="status-time" v-if="row.sendTime">
              {{ formatTime(row.sendTime) }}
            </div>
          </div>
        </template>
      </fks-table-column>

      <fks-table-column prop="errorMsg" label="失败原因" width="120" align="center" v-if="hasError">
        <template slot-scope="{ row }">
          <span v-if="row.status === 'failed' && row.errorMsg">
            {{ row.errorMsg }}
          </span>
          <span v-else>-</span>
        </template>
      </fks-table-column>

      <fks-table-column label="操作" width="100" align="center">
        <template slot-scope="{ row }">
          <fks-button
            v-if="row.status === 'failed'"
            :disabled="type === 'view'"
            type="text"
            size="small"
            @click="handleResend(row)"
          >
            重新发送
          </fks-button>
          <span v-else>-</span>
        </template>
      </fks-table-column>
    </fks-table>
  </fks-dialog>
</template>

<script>
// todo 表单状态为审批状态下才能重发短信，view模式下只读
export default {
  name: 'SmsStatusDialog',
  props: ['type'],
  data() {
    return {
      visible: false,
      smsData: [
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市西湖区南北大塘纪念碑浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
        {
          status: 'success',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '张三',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
        },
        {
          status: 'failed',
          approvalStep: '调度员派车',
          notifyRole: '联系人',
          notifyTarget: '李四',
          phone: '158****1736',
          content:
            '【华东院】派车管理系统提醒：您有一个用车行程，03月24日 16:00出发，浙江省杭州市上城区杭州东站——＞浙江省杭州市西湖区南北大塘纪念碑，车牌号浙A55T3S，司机周碧云(15267047039)。如有疑问请联系申请人周碧云254(15267047039)。详情 daminproxy.hdec.com/xmb/#/xcck/60781',
          sendTime: '2025-07-11 09:45:03',
          errorMsg: '通道异常',
        },
      ],
    }
  },
  computed: {
    dialogWidth() {
      return window.innerWidth < 768 ? '95%' : '1200px'
    },
    hasError() {
      return this.smsData.some((item) => item.status === 'failed')
    },
    failedCount() {
      return this.smsData.filter((item) => item.status === 'failed').length
    },
    successCount() {
      return this.smsData.filter((item) => item.status === 'success').length
    },
  },
  methods: {
    handleOpen() {
      this.visible = true;
    },
    handleClose() {
      this.visible = false;
      this.$emit('close')
    },

    getStatusType(status) {
      const statusMap = {
        success: 'success',
        failed: 'danger',
        sending: 'warning',
        pending: 'info',
      }
      return statusMap[status] || 'info'
    },

    getStatusColor(status) {
      const colorMap = {
        success: '#52c41a',
        failed: '#ff4d4f',
        sending: '#faad14',
        pending: '#1890ff',
      }
      return colorMap[status] || '#d9d9d9'
    },

    getStatusIcon(status) {
      const iconMap = {
        success: 'fks-icon-check-circle',
        failed: 'fks-icon-close-circle',
        sending: 'fks-icon-loading',
        pending: 'fks-icon-clock-circle',
      }
      return iconMap[status] || 'fks-icon-question-circle'
    },

    getStatusText(status) {
      const textMap = {
        success: '发送成功',
        failed: '发送失败',
        sending: '发送中',
        pending: '待发送',
      }
      return textMap[status] || '未知状态'
    },

    formatTime(time) {
      if (!time) return '-'
      try {
        const date = new Date(time)
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        })
      } catch (error) {
        return time
      }
    },

    handleResend(row) {
      this.$emit('resend', row)
      this.$message.info(`正在重新发送短信给 ${row.notifyTarget}...`)
    },

    handleResendAll() {
      const failedItems = this.smsData.filter((item) => item.status === 'failed')
      if (failedItems.length === 0) {
        this.$message.warning('没有发送失败的短信需要重新发送')
        return
      }

      this.$confirm(`确定要重新发送 ${failedItems.length} 条失败的短信吗？`, '确认重新发送', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$emit('resend-all', failedItems)
          this.$message.info(`正在重新发送 ${failedItems.length} 条短信...`)
        })
        .catch(() => {
          // 用户取消
        })
    },
  },
}
</script>

<style lang="less" scoped>
::v-deep(.fks-dialog__body) {
  padding: 40px !important;
}
/deep/ .fks-table--enable-row-hover .fks-table__body tr:hover>td {
  background-color: #f5f7fa !important; /* 替换为你想要的颜色 */
}
.sms-status-dialog-wrapper {
  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .dialog-title {
      font-size: 32px;
      color: #333333;
    }

    .dialog-subtitle {
      font-size: 28px;
      color: #ff3f4c;
    }

    .dialog-actions {
      display: flex;
      align-items: center;
      margin-top: 8px;

      .fks-button {
        color: #1890ff;

        &:hover {
          color: #40a9ff;
        }

        .fks-icon-refresh {
          margin-right: 4px;
        }
      }
    }
  }
}

.sms-content {
  text-align: left !important;
}

.status-cell {
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 4px;

  .status-time {
    font-size: 24px;
    color: #555555;
  }

  .status-text {
    display: flex;
    align-items: center;

    .success {
      font-size: 28px;
      color: #40bb5a;
    }
  }
}
</style>
