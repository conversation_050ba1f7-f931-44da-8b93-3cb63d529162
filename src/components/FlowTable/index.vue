<template>
  <div>
    <fks-table
      :data="data"
      :header-cell-style="{
        background: 'transparent',
        color: '#333333aa !important',
        fontWeight: 'unset !important',
      }"
      :cell-style="{ color: '#333333 !important' }"
    >
      <fks-table-column
        header-align="left"
        align="left"
        prop="taskName"
        label="节点名称"
        width="150"
      />
      <fks-table-column header-align="left" align="left" prop="assigneeName" label="审批人" />
      <fks-table-column
        header-align="left"
        align="left"
        prop="approveStateName"
        label="审批结果"
        width="100"
      >
        <template slot-scope="{ row }">
          <card-tag
            style="line-height: 12px"
            :tag="{ text: processText(row.approveStateName), color: getTagColor(row) }"
          />
        </template>
      </fks-table-column>
      <fks-table-column header-align="left" align="left" prop="comment" label="审批意见">
        <template slot-scope="{ row }">
          <overflow-tooltip v-if="row.comment" :text="row.comment" />
        </template>
      </fks-table-column>
      <fks-table-column
        header-align="left"
        align="left"
        prop="approveDate"
        label="审批时间"
        width="180px"
      >
        <template slot-scope="{ row }">
          <span v-if="row.approveDate">{{
              $dayjs(row.approveDate).format('YYYY-MM-DD HH:mm')
            }}</span>
        </template>
      </fks-table-column>
      <!-- 短信通知状态列 -->
      <fks-table-column
        v-if="shouldShowSmsColumn"
        header-align="left"
        align="left"
        label="短信通知状态"
        width="120"
      >
        <template slot-scope="{ row }">
          <div class="status-cell">
            <img
              :src="require('@/assets/img/sms/send-success.svg')"
              width="14px"
              height="14px"
              alt="#"
            />
            <span class="m-l-10 success" @click="openSmsDialog(row)">发送成功</span>
          </div>
        </template>
      </fks-table-column>
    </fks-table>

    <!-- 短信通知状态弹窗 -->
    <sms-status-dialog ref="smsStatusDialogRef" :type="type" />
  </div>
</template>

<script>
import CardTag from '@components/CardFlow/components/tag.vue'
import OverflowTooltip from '@components/OverflowTooltip/index.vue'
import SmsStatusDialog from '@components/SmsStatusDialog/index.vue'
import { getTaskNumber, UserTask_200, UserTask_500 } from '@utils/constants'
import { getUserTaskByFormId } from '@modules/Todo/api'
import storage from '@utils/storage'

export default {
  name: 'FlowTable',
  components: { OverflowTooltip, CardTag, SmsStatusDialog },
  props: ['data', 'bizId', 'type'],
  data() {
    return {
      shouldShowSmsColumn: false
    }
  },
  computed: {
    currentUserIsDispatcher() {
      if (Array.isArray(this.data) && this.data.length) {
        const dispatcher1 = this.data.find(item => item.taskKey === UserTask_200); // 【车辆调度员】派车
        const dispatcher2 = this.data.find(item => item.taskKey === UserTask_500); // 【车辆调度员】审批费用
        const username = storage.get('username')
        if (dispatcher1) {
          return dispatcher1.assignee === username
        } else if (dispatcher2) {
          return dispatcher2.assignee === username
        } else {
          return false
        }
      }
      return false;
    }
  },
  methods: {
    getTagColor(row) {
      const text = row.approveStateName
      switch (text) {
        case '提交':
          return '#9BA0A3'
        case '通过':
          return '#03BE8A'
        case '待办':
          return '#3C83FF'
        case '退回':
          return '#FF3F4C'
        case '委托':
          return '#FFA01E'
        case '变更':
          return '#FFA01E'
        default:
          return '#3C83FF'
      }
    },
    processText(text) {
      if (text === '委托') {
        return '变更'
      }
      return text
    },
    openSmsDialog() {
      this.$refs.smsStatusDialogRef.handleOpen()
    }
  },
  created() {
    getUserTaskByFormId(this.bizId).then((res) => {
      if (res.status) {
        const data = res.data ? res.data[this.bizId] : ''
        if (data) {
          const taskNumber = getTaskNumber(data.taskKey)
          // 流程在【车辆调度员派车】之后 且 用户是车辆调度员才能显示
          this.shouldShowSmsColumn = Number(taskNumber) >= 300 && this.currentUserIsDispatcher // 300代表【司机确认】
        }
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.status-cell {
  display: flex;
  align-items: center;

  span {
    text-decoration: underline;
    text-underline-offset: 8px;
    cursor: pointer;
  }

  .success {
    font-size: 28px;
    color: #40bb5a;
  }
}
</style>
