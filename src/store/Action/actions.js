/*
 * @Description:
 * @Author: ye_xf
 * @Date: 2022-07-26 13:43:47
 * @LastEditTime: 2022-08-05 15:26:42
 * @LastEditors: ye_xf
 * @Reference:
 */
import * as types from './actionTypes'
import {GET_DATA_BASE_PORTAL, LOADING_USER_DATA} from './actionTypes'
import * as mutationTypes from '../Mutation/mutationTypes'
import {SET_OPEN_ID} from '../Mutation/mutationTypes'
import * as StateTypes from '@/store/State/stateTypes'
import {getEnums} from '@/api/app'
import {login} from '@/api/login'
import {getAuthPortalList, getPermissions, getPortals, getUser, getFavoritePortals, portalsFavorite} from '@/api/user'
import storage from '@/utils/storage'
import {PORTAL_ID} from "@/config/app/develop"
import router from '@/router'
import {getUserRoutes} from "@utils/route";
import {app_id, isUserTokenExpired} from "@utils/feishuapi";
import {isJSON} from '@/utils/util'
import request from '@/utils/request'
import * as stateTypes from "@store/State/stateTypes";
import defaultSettings from "@/config";
import {COMPANY_PORTAL} from "@utils/constants";
import {getCommonlyUsedProjectList, getProjectStatus} from '@components/HomeHeader/api'


const actions = {
  //用户登录
  [types.USER_LOGIN]({commit, dispatch}, data) {
    return new Promise((resolve, reject) => {
      login(data)
        .then((res) => {
          if (res) {
            //加载用户信息
            commit(mutationTypes.SET_USER_LOCAL_INFO, res);
            commit(mutationTypes.SET_USER_INFO, res)
            dispatch(types.LOGIN_SUCCESS, res)
            if (res) {
              return resolve(res)
            }
          }
          reject('')
        })
        .catch((e) => {
          reject(e)
        })
    })
  },

  //获取枚举
  [types.GET_ENUMS]({commit}) {
    return new Promise((resolve, reject) => {
      getEnums()
        .then((res) => {
          if (res.status && res.data) {
            commit(mutationTypes.SET_ENUM, res.data)
          }
          resolve(res)
        })
        .catch((err) => reject(err))
    })
  },

  //获取语言资源
  [types.GET_LANG]({commit}, lang_config) {
    getLang(lang_config).then(res => {
      if (res.status && res.data) {
        commit(mutationTypes.SET_ONLINE_LANG, {...res.data})
      }
    }).catch(() => {
    })
  },

  [types.GET_PERMISSIONS]({state, commit}, menuId) {
    //获取按钮权限
    return new Promise((resolve, reject) => {
      getPermissions(menuId).then(res => {
        if (res.status) {
          commit(mutationTypes.SET_PERMISSION, {key: menuId, value: res.data})
        }
        resolve(true)
      }).catch(() => {
        resolve(true)
      })
    })
  },
  //获取门户
  [types.GET_PORTALS]({state, commit, dispatch}) {
    return new Promise((resolve, reject) => {
      getPortals().then(res => {
        let portals = []
        if (res.data instanceof Array && res.data.length > 0) {
          portals = res.data.sort((f, b) => {
            return f.type - b.type
          })
        }
        resolve(portals)
      }).catch(err => {
        resolve([])
      })
    })
  },
  //获取收藏门户
  [types.GET_FAVORITE_PORTALS]({state, commit, dispatch}) {
    return new Promise((resolve, reject) => {
      getFavoritePortals().then(res => {
        if (res.status) {
          commit(mutationTypes.SET_FAVORITE_PORTALS, res.data)
        }
        resolve(res.data || [])
      }).catch(err => {
        resolve([])
      })
    })
  },
  //收藏门户
  [types.PORTALS_FAVORITE]({state, commit}, params) {
    return new Promise((resolve, reject) => {
      portalsFavorite(params).then(res => {
        resolve(true)
      }).catch(err => {
        resolve(false)
      })
    })
  },
  //门户切换
  [types.CHANGE_PORTAL]({commit}, portal) {
    commit(mutationTypes.CLEAN_VIEWS)
    commit(mutationTypes.CLEAN_ROUTE)
    commit(mutationTypes.CLEAN_PERMISSION)
    commit(mutationTypes.CLEAN_PERMISSION_QUEUE)
    commit(mutationTypes.UPDATE_PORTAL, portal)
  },

  async [types.GET_AUTH_PORTAL]({commit}) {
    return new Promise((resolve, reject) => {
      getAuthPortalList().then(res => {
        if (res.status) {
          // console.log(res,'====================获取的有权限的列表')
          commit(mutationTypes.SET_AUTH_PORTAL, res.data || [])
        }
        resolve(true)
      }).catch(() => {
        resolve(true)
      })
    })
  },
  //根据门户获取用户信息
  [types.UPDATE_USER]({commit}, portalId = PORTAL_ID) {
    return new Promise((resolve, reject) => {
      getUser({userName: `${storage.get('username')}`, portalId}).then(res => {
        if (res.status && res.data) {
          commit(mutationTypes.SET_USER_INFO, res.data)
        }
        resolve(res)
      }).catch(e => resolve(e))
    })
  },
  // 返回用户信息后
  async [types.LOGIN_SUCCESS]({dispatch, commit, rootGetters, state}, res) {
    await dispatch(LOADING_USER_DATA, res);
    await getUserRoutes();
    await dispatch('CarApply/getCurrentUser', res.userName);
    const response = await dispatch('CarApply/getThirdPartyUserInfo', res.userName);
    const thirdPartyList = response.data;
    console.info('🚀🚀', 'LOGIN_SUCCESS thirdPartyList -->', thirdPartyList, `<-- actions.js/`)
    await dispatch('CarApply/getEnum');
    commit(mutationTypes.IS_LOGIN, true)
    if (thirdPartyList && Array.isArray(thirdPartyList)) {
      const item = thirdPartyList.find(item => item.tpUserType === 1);
      if (item) {
        commit(SET_OPEN_ID, item.tpOpenId)
        storage.set('thirdPartyUser', JSON.stringify(item));
        // 获取乘车人列表token，为后续获取token做铺垫
        if (state[StateTypes.APPLY_RESOURCE] === 1) {
          // 飞书

          // console.info('🚀🚀', 'LOGIN_SUCCESS r2 -->', r2, `<-- actions.js/`)
        } else if (state[StateTypes.APPLY_RESOURCE] !== 1) {
          // 微信公众号或pc
          try {
            const expireInfo = await isUserTokenExpired(item.tpOpenId);
            console.log('在微信公众号或pc：', expireInfo)
            if (!expireInfo.status) {
              // 过期或失效则需要重新登录
              window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${app_id}&redirect_uri=${window.encodeURIComponent(window.location.href.split('?')[0].split('#')[0])}`
            }
          } catch (e) {
            if (!e.data.status && e.datacode === -8000170) {
              window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${app_id}&redirect_uri=${window.encodeURIComponent(window.location.href.split('?')[0].split('#')[0])}`
            }
            console.log('===在微信公众号或pc：===', e)
          }

        }
      }
    }
    if (router.currentRoute.path === '/' || router.currentRoute.path === '/sign' || router.currentRoute.path === '/login') {
      const to1 = JSON.parse(storage.get('feishu_link'))
      console.info('🚀🚀', 'to1 -->', to1, `<-- action.js`)
      // 登录成功后，直接跳转到项目门户的默认首页（因为数据舱门户没有菜单数据，会导致页面加载不出来，而数据舱的菜单都放在了项目门户）
      const defaultRoute = state[stateTypes.PLATFORM] === 'pc' ? defaultSettings.projectPortalDefaultRoute : defaultSettings.mobileProjectPortalDefaultRoute;
      await router.push(to1 || defaultRoute)
    }
  },
  [types.GET_RECENTLY_USED_PORTALS]({commit}) {
    // 获取常用项目
    getCommonlyUsedProjectList().then(res => {
      if (res.status) {
        commit(mutationTypes.SET_RECENTLY_USED_PORTALS, res.data);
      }
    })
  },
  //加载用户信息
  [types.LOADING_USER_DATA]({commit, rootGetters, dispatch, state}, data) {
    return new Promise(async (resolve, reject) => {
      //登录时，保存token
      if (data) {
        storage.set('username', data.userName)
        storage.set('user', JSON.stringify(data))
        data.access_token && storage.set('access_token', data.access_token)
      }

      //在非登录页刷新时需要判断token
      if (!storage.get('access_token') || 'undefined' == storage.get('access_token')) {
        resolve(true)
        return
      }
      // 登录后获取当前账户有权限的项目列表
      await dispatch(GET_DATA_BASE_PORTAL)
      await dispatch(types.GET_AUTH_PORTAL)
      // 获取最近使用的门户
      await dispatch(types.GET_RECENTLY_USED_PORTALS)
      //当前应用为多门户模式
      await dispatch(types.GET_FAVORITE_PORTALS) // 获取收藏的门户
      let portalList = await dispatch(types.GET_PORTALS)
      if (portalList.length > 0) {
        // 如果门户列表中，项目门户的子门户一个都没有，则重定向到404页面
        const hasNoPortal = !portalList.some(portal => portal.type === 3 && portal.parentName === '项目门户');
        if (hasNoPortal) {
          // 如果没有门户，直接跳转到页
          await router.push('/emptyPortal');
          resolve(true)
        } else {
          commit(mutationTypes.SET_PORTALS, portalList)

          let portal = ''
          const isjson = isJSON(`${storage.get('portal')}`);
          if (isjson) {
            portal = JSON.parse(`${storage.get('portal')}`);
          }
          if (!portal) {
            // 1. 默认中收藏项目里选第一个
            if (state[stateTypes.FAVORITE_PORTALS].length > 0) {
              // 从收藏项目里选
              const firstPortal = state[stateTypes.FAVORITE_PORTALS][0];
              portal = portalList.find(item => item.id === firstPortal);
            } else if (state[stateTypes.RECENTLY_USED_PORTALS].length > 0) {
            // 2. 选中常用项目第一个，没有的话选中列表第一个
              // 从常用项目里选
              portal = state[stateTypes.RECENTLY_USED_PORTALS].find(item => item.type === 3 && item.name !== COMPANY_PORTAL)
            } else {
              // 3. 本地没有默认门户，则默认选中【项目门户】中的第一个项目
              portal = portalList.find(item => item.type === 3 && item.name !== COMPANY_PORTAL);
            }
          }
          // 设置二级门户，公司门户本身为二级门户，所以直接赋值；项目门户的子门户为三级门户，所以需要找到其二级门户
          const portalName = portal.type === 3 ? portal.parentName : portal.name;
          commit(mutationTypes.SET_SECOND_LEVEL_PORTAL, portalName);
          getProjectStatus(portal.id).then(res => {
            if (res.status) {
              // 100 正常，200 关闭
              const status = res.data ? (res.data.projectStatus === 200) : false; // 数据舱门户不设置为关闭
              commit(mutationTypes.SET_PROJECT_CLOSED, status)
              commit(mutationTypes.SET_VD_PROJECT_INFO, res.data)
            }
          })
          dispatch(types.CHANGE_PORTAL, portal)
          resolve(true)
        }
      } else {
        // 如果没有门户，直接跳转到页
        await router.push('/emptyPortal');
        resolve(true)
      }

    }).catch(e => {
      return false
    })
  },
  async [types.QUERY_GLOBAL_TABLE_DATA]({commit}, data) {
    commit(mutationTypes.SET_TABLE_LOADING, true)
    const res = await request(data)
    commit(mutationTypes.SET_QUERY_TABLE_DATA, res.data)
    commit(mutationTypes.SET_TABLE_LOADING, false)
  },
  async [types.GET_DATA_BASE_PORTAL]({commit}){
    const res = await request({
      url: '/vehicle-dispatch/vd/system/info',
      method: 'get'
    })
    commit(mutationTypes.SET_DATA_BASE_PORTAL, res.data)
  },
  async [types.UPDATE_PROJECT_STATUS]({commit, state}, payload) {
    const {projectId} = payload;
    // 检查状态表里是否有可用缓存
    const status = state[stateTypes.PORTAL_STATUS_TABLE][projectId];
    if (!status) {
      const res = await getProjectStatus(projectId);
      if (res.status) {
        commit(mutationTypes.SET_PORTAL_STATUS_TABLE, { [projectId]: res.data.projectStatus })
      }
    }
  }
}

export default actions
