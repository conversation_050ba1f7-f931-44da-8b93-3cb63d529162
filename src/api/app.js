/*
 * @Author: <EMAIL>
 * @Date: 2019-11-07 09:36:15
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-11 14:26:19
 * @Description: 应用相关接口
 */
import request from '@/utils/request'
import { resetMessage } from '@/utils/message'
import Qs from 'qs'

/**
 * @description: 获取token
 * @param {*}
 * @return {*}
 */
export function getToken (data) {
  return request({
    url: '/sys-auth/oauth/token',
    method: 'post',
    data: {
      scope: 'all',
      ...data
    },
    transformRequest: [function (data) {
      data = Qs.stringify(data) //序列化参数
      return data
    }]
  }).catch(e => {
    resetMessage.warning(e.response.data)
  })
}
/**
 * @description: 登出
 * @param {*}
 * @return {*}
 */
/** */
export function loginOut () {
  return request({
    url: '/sys-auth/oauth/exit',
    method: 'delete'
  })
}
//埋点
export function addMonitor (data) {
  return request({
    url: '/sys-monitor/analysis',
    method: 'post',
    data: data,
    sign: true,
  })
}

//新增浏览记录
export function setBrowseRecords (data) {
  return request({
    url: '/qingluan/browse',
    method: 'post',
    pass: false,
    data,
  })
}

// 查询字典接口
export function getEnums () {
  return request({
    url: 'sys-system/dictionary/detail/list',
    method: 'GET',
    sign: true,
  })
}

/**
 * @description: 获取服务器时间戳
 * @param {*}
 * @return {*}
 */
export function getTs () {
  return request({
    method: 'GET',
    // url: '/sys-gateway/sign/ts',
    url: '/sign/ts',
    timeout: 1000,
    cMsg: true
  })
}
