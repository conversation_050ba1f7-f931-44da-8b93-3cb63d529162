import axios from 'axios'
import posix from "@utils/path";
import storage from '@utils/storage'
/*
 * @Author: <EMAIL>
 * @Date: 2019-11-01 16:43:30
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2022-04-07 14:01:31
 * @Description: 工具类，根据功能划分
 */
export const isEmpty = (val) => {
  // null or undefined
  if (val == null) return true

  if (typeof val === 'boolean') return false

  if (typeof val === 'number') return !val

  if (val instanceof Error) return val.message === ''

  switch (Object.prototype.toString.call(val)) {
    // String or Array
    case '[object String]':
    case '[object Array]':
      return !val.length

    // Map or Set or File
    case '[object File]':
    case '[object Map]':
    case '[object Set]': {
      return !val.size
    }
    // Plain Object
    case '[object Object]': {
      return !Object.keys(val).length
    }
    default:
      return false
  }
}

/**
 *
 * @param {*} func 执行防抖的回调函数
 * @param {*} wait timeout时长，默认500ms
 * @param {*} immediate 是否立即执行，默认false
 * @returns
 */
export function debounce(func, wait = 500, immediate = false, ctx) {
  let timeout, result

  const debounced = function () {
    let args = arguments
    if (timeout) clearTimeout(timeout)
    //允许立即执行
    if (immediate) {
      // 如果已经执行过，不再执行
      let callNow = !timeout
      // wait对应时间间隔内，callNow一直为false，不会触发func；经过wait时长后置空timeout，再次执行时callNow为true
      timeout = setTimeout(function () {
        timeout = null
      }, wait)
      if (callNow) result = func.apply(ctx, args)
    } else {
      timeout = setTimeout(function () {
        func.apply(ctx, args)
      }, wait)
    }
    return result
  }

  //支持取消防抖
  debounced.cancel = function () {
    clearTimeout(timeout)
    timeout = null
  }

  return debounced
}


//判断数据是否为json
export function isJSON(str) {
  if (typeof str == 'string') {
    try {
      let obj = JSON.parse(str)
      if (typeof obj == 'object' && obj) {
        return true
      } else {
        return false
      }
    } catch (e) {
      console.info('🚀🚀', 'e -->', e, `<-- util.js/isJSON`)
      return false
    }
  }
}

/**
 * @description: 执行js表达式，兼容性实现eval()
 * @param {type}
 * @return:
 */
export function evil(fn) {
  let Fn = Function // 一个变量指向Function，防止有些前端编译工具报错
  return new Fn('return ' + fn)()
}

/**
 * @description: 获取url中的query
 * @param {variable} 需要获取的key值
 */
export function getQueryVariable(variable) {
  let rawQuery = (window.location.search ? window.location.search.substring(1) : '') || window.location.href.split('?')[1]
  if (!rawQuery) {
    return null
  }
  let reg = new RegExp('(^|&)' + variable + '=([^&]*)(&|$)')
  let r = rawQuery.match(reg)//search,查询？后面的参数，并匹配正则
  if (r != null) {
    return decodeURIComponent(r[2])
  }
  return null
}

export function getAbsoluteUrl(url) {
  let a = {}
  a = document.createElement('A')
  a.href = url // 设置相对路径给Image, 此时会发送出请求
  url = a.href // 此时相对路径已经变成绝对路径
  return url
}

/**
 * @description: 获取url参数对象或字符串
 * @param {String} url
 * @param {String} 指定参数
 * @return {type}
 */
export function getParamByUrl(url, par) {
  let fileUrl = '' //文件地址
  let listParam = '' //参数集合
  let listParamObj = {} //
  let listParamArr = '' //包含所有参数
  //去掉hash
  url = url.replace('#', '')
  //获取文件地址
  fileUrl = url.split('?')[0]
  listParam = url.split('?')
  if (listParam.length > 1) {
    listParam.shift()
    let listParam2 = listParam.join()
    listParamArr = listParam2.split('&')
    listParamArr.forEach(function (ele) {
      let temp = ele.split('=')
      listParamObj[temp[0]] = temp[1]
    })
  } else {
    console.log('没有参数')
  }
  if (par == 'all') {
    //返回全部参数
    return listParamObj
  } else {
    //返回指定参数
    for (const key in listParamObj) {
      if (key == par) {
        return JSON.parse('{' + '"' + par + '"' + ':' + '"' + listParamObj[par] + '"' + '}')
      } else {
        console.log('没有传入的参数')
      }
    }
  }
}

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/-[a-z]/g, str1 => str1.substr(-1).toUpperCase())
}

function stringify(obj) {
  return JSON.stringify(obj, (key, val) => {
    if (typeof val === 'function') {
      return `${val}`
    }
    return val
  })
}

function parse(str) {
  JSON.parse(str, (k, v) => {
    if (v.indexOf && v.indexOf('function') > -1) {
      return eval(`(${v})`)
    }
    return v
  })
}

export function jsonClone(obj) {
  return JSON.parse(stringify(obj))
}


// 深拷贝对象
export function deepClone(obj, hash = new WeakMap()) {
  const _toString = Object.prototype.toString

  // null, undefined, non-object, function
  if (!obj || typeof obj !== 'object') {
    return obj
  }

  // DOM Node
  if (obj.nodeType && 'cloneNode' in obj) {
    return obj.cloneNode(true)
  }

  // Date
  if (_toString.call(obj) === '[object Date]') {
    return new Date(obj.getTime())
  }

  // RegExp
  if (_toString.call(obj) === '[object RegExp]') {
    const flags = []
    if (obj.global) {
      flags.push('g')
    }
    if (obj.multiline) {
      flags.push('m')
    }
    if (obj.ignoreCase) {
      flags.push('i')
    }

    return new RegExp(obj.source, flags.join(''))
  }
  if (hash.get(obj)) {
    return hash.get(obj)
  }
  const result = Array.isArray(obj) ? [] : obj.constructor ? new obj.constructor() : {}
  hash.set(obj, result)
  for (const key in obj) {
    result[key] = deepClone(obj[key], hash)
  }

  return result
}


//获取宽度
export var getWidth = function getWidth(elem) {
  var width = elem && typeof elem.getBoundingClientRect === 'function' && elem.getBoundingClientRect().width
  if (width) {
    width = +width.toFixed(6)
  }
  return width || 0
}

//设置样式
export var setStyle = function setStyle(elem, styleProperty, value) {
  if (elem && typeof (elem.style) === 'object') {
    elem.style[styleProperty] = value
  }
}

export function isMobile() {
  return navigator.userAgent.includes('Android') || navigator.userAgent.includes('iPhone')
}

/**
 * @description 生成UUID
 * @param {Integer} len uuid长度
 * @param {Integer} radix uuid基数
 * @returns {String} UUID
 */
export function uuid(len, radix) {
  let chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
  let uuid = [],
    i
  radix = radix || chars.length

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix]
  } else {
    // rfc4122, version 4 form
    let r
    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
    uuid[14] = '4'
    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | Math.random() * 16
        uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r]
      }
    }
  }
  return uuid.join('')
}

/**
 * 加载一个远程脚本
 * @param {String} src 一个远程脚本
 * @param {Function} callback 回调
 */
const callbacks = {}

export function loadScript(src, callback) {
  const existingScript = document.getElementById(src)
  const cb = callback || (() => {
  })
  if (!existingScript) {
    callbacks[src] = []
    const $script = document.createElement('script')
    $script.src = src
    $script.id = src
    $script.async = 1
    document.body.appendChild($script)
    const onEnd = 'onload' in $script ? stdOnEnd.bind($script) : ieOnEnd.bind($script)
    onEnd($script)
  }

  callbacks[src].push(cb)

  function stdOnEnd(script) {
    script.onload = () => {
      this.onerror = this.onload = null
      callbacks[src].forEach(item => {
        item(null, script)
      })
      delete callbacks[src]
    }
    script.onerror = () => {
      this.onerror = this.onload = null
      cb(new Error(`Failed to load ${src}`), script)
    }
  }

  function ieOnEnd(script) {
    script.onreadystatechange = () => {
      if (this.readyState !== 'complete' && this.readyState !== 'loaded') return
      this.onreadystatechange = null
      callbacks[src].forEach(item => {
        item(null, script)
      })
      delete callbacks[src]
    }
  }
}


/**
 *
 * 参数处理的工具类
 */
// 生成guid
export const guid = () => {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    let r = (Math.random() * 16) | 0;
    let v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

/**
 * 对象转url参数
 * @param {*} data,对象
 * @param {*} isPrefix,是否自动加上"?"
 */
export function queryParams(data = {}, isPrefix = true, arrayFormat = 'brackets') {
  let prefix = isPrefix ? '?' : ''
  let _result = []
  if (['indices', 'brackets', 'repeat', 'comma'].indexOf(arrayFormat) == -1) arrayFormat = 'brackets';
  for (let key in data) {
    let value = data[key]
    // 去掉为空的参数
    if (['', undefined, null].indexOf(value) >= 0) {
      continue;
    }
    // 如果值为数组，另行处理
    if (value.constructor === Array) {
      // e.g. {ids: [1, 2, 3]}
      switch (arrayFormat) {
        case 'indices':
          // 结果: ids[0]=1&ids[1]=2&ids[2]=3
          for (let i = 0; i < value.length; i++) {
            _result.push(key + '[' + i + ']=' + value[i])
          }
          break;
        case 'brackets':
          // 结果: ids[]=1&ids[]=2&ids[]=3
          value.forEach(_value => {
            _result.push(key + '[]=' + _value)
          })
          break;
        case 'repeat':
          // 结果: ids=1&ids=2&ids=3
          value.forEach(_value => {
            _result.push(key + '=' + _value)
          })
          break;
        case 'comma':
          // 结果: ids=1,2,3
          let commaStr = "";
          value.forEach(_value => {
            commaStr += (commaStr ? "," : "") + _value;
          })
          _result.push(key + '=' + commaStr)
          break;
        default:
          value.forEach(_value => {
            _result.push(key + '[]=' + _value)
          })
      }
    } else {
      _result.push(key + '=' + value)
    }
  }
  return _result.length ? prefix + _result.join('&') : ''
}


export function handleUrl(url) {
  let obj = {}
  let arrNew = url.split("?")[1].split("&")
  for (let i = 0; i < arrNew.length; i++) {
    let key = arrNew[i].split("=")[0]
    let value = arrNew[i].split("=")[1]
    //在对象中添加属性
    obj[key] = value
  }
  return obj
}

// 扁平化树
export function treeToArray(tree) {
  let res = []
  for (const item of tree) {
    const {children, ...i} = item
    if (children && children.length) {
      res = res.concat(treeToArray(children))
    }
    res.push(i)
  }
  return res
}

// 遍历寻找
export function traverse(data, callback, property = 'children') {
  let flag = true;
  const recursiveFind = data => {
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      const stopFind = flag && callback(item);
      if (stopFind) {
        flag = false;
        break;
      } else {
        if (item[property] && item[property].length && flag) {
          recursiveFind(item[property]);
        }
      }
    }
  };
  recursiveFind(data);
}

// 倒计时
export function startCountdown(seconds, callback, countingCallback) {
  let remainingSeconds = seconds;

  const countdownInterval = setInterval(() => {
    if (remainingSeconds <= 0) {
      clearInterval(countdownInterval);
      callback instanceof Function && callback()
      countingCallback instanceof Function && countingCallback(remainingSeconds)
    } else {
      countingCallback instanceof Function && countingCallback(remainingSeconds)
      remainingSeconds--;
    }
  }, 1000);
}

export function hasNewVersion() {
  const path = posix.join(window.location.host, window.location.pathname, 'static/version.json')
  return axios.get(`//${path}`).then(res => {
    const version = res.data.version
    const localVersion = localStorage.getItem('version')
    if (localVersion !== 'undefined' && (Number(localVersion) !== Number(version))) {
      // 将当前门户取出保存，再恢复
      const portal = storage.get('portal');
      localStorage.clear();
      if (portal) {
        storage.set('portal', portal);
      }
      localStorage.setItem('version', version)
      console.log('有新版本，重新刷新页面')
      window.location.reload()
    } else {
      localStorage.setItem('version', version)
    }
  })
}

// 文件图标
export function fileIcon (name) {
  const data = name + ''
  switch (data.substring(data.lastIndexOf('.')).toLowerCase()) {
    case '.dir':
      return 'doc_folder'
    case '.docx':
      return 'doc_word'
    case '.doc':
      return 'doc_word'
    case '.pdf':
      return 'doc_pdf'
    case '.xlsx':
      return 'doc_excel'
    case '.rar':
      return 'doc_zip'
    case '.zip':
      return 'doc_zip'
    case '.ppt':
      return 'doc_ppt'
    case '.pptx':
      return 'doc_ppt'
    case '.txt':
      return 'doc_txt'
    case '.html':
      return 'doc_html'
    case '.exe':
      return 'doc_exe'
    case '.jpg':
    case '.png':
    case '.jpeg':
    case '.gif':
    case '.bmp':
    case '.svg':
    case '.gif':
    case '.webp':
    case '.psd':
    case '.tiff':
      return 'doc_img'
    case '.amr':
    case '.wav':
      return 'doc_music'
    case '.mp4':
    case '.mov':
      return 'doc_video'
    default:
      return 'doc_blank'
  }
}

export function deepEqual(x, y) {
  const ok = Object.keys, tx = typeof x, ty = typeof y;
  return x && y && tx === 'object' && tx === ty ? (
    ok(x).length === ok(y).length &&
    ok(x).every(key => deepEqual(x[key], y[key]))
  ) : (x === y);
}

export function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function duplicateObjectToArray(obj, num) {
  const arr = [];
  for (let i = 0; i < num; i++) {
    arr.push(obj)
  }
  return arr;
}

export function getRandomNumber(a, b) {
  // 确保 a 是最小值，b 是最大值
  const min = Math.ceil(Math.min(a, b));
  const max = Math.floor(Math.max(a, b));
  // 生成 min 到 max 之间的随机整数
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

export const richTextGenerator = (name, value) => {
  return `{${name}|${value}}`;
};

export function hexToRgba(hex, opacity) {
  return (
    'rgba(' +
    parseInt('0x' + hex.slice(1, 3)) +
    ',' +
    parseInt('0x' + hex.slice(3, 5)) +
    ',' +
    parseInt('0x' + hex.slice(5, 7)) +
    ',' +
    opacity +
    ')'
  );
}

export const isChinese = (str) => {
  return /[\u4E00-\u9FA5]+/g.test(str)
}

export const colorList = [
  ['#6CC7FF', '#306BFE'],
  ['#1AC477', '#73F0A1'],
  ['#B3A0FE', '#BFB0FC'],
  ['#3BCFF4', '#8CE8FF'],
  '#399AEA',
  '#ADDD52',
  '#FFCB27',
  '#49dff0',
  '#6f81da',
  '#00ffb4',
  '#46DAFF',
  '#c487ee',
  '#deb140',
  '#c23531',
  '#2f4554',
  '#61a0a8',
  '#d48265',
  '#91c7ae',
  '#749f83',
  '#ca8622',
  '#bda29a',
  '#6e7074',
  '#546570',
  '#c4ccd3'
];

export const fakeResponse = (d) => {
  return {
    status: true,
    data: d
  }
}

export const fakeAwait = async () => {
  const timeout = getRandomNumber(1000, 3000)
  await sleep(timeout)
}

/**
 * @description: 解析列表为树形
 * @param {Array} data 原始列表
 * @param {String} idName id标识值
 * @param {String} pidName pid标识值
 * @return: val 树形列表 map 对应map
 */
export function transTree(data, idName, pidName) {
  data.forEach((item) => {
    delete item.children
  })
  // 将数据存储为 以 id 为 KEY 的 map 索引数据列
  let map = {}
  data.forEach((item) => {
    map[item[idName]] = item
  })
  let val = []
  data.forEach((item) => {
    // 以当前遍历项，的pid,去map对象中找到索引的id
    let parent = map[item[pidName]]
    // 如果找到索引，那么说明此项不在顶级当中,那么需要把此项添加到，他对应的父级中
    if (parent) {
      (parent.children || (parent.children = [])).push(item)
    } else {
      // 如果没有在map中找到对应的索引ID,那么直接把 当前的item添加到 val结果集中，作为顶级
      val.push(item)
    }
  })
  return {
    val,
    map
  }
}

/**
     * @description: 解析字符串（数字）数组中新增的内容
     * @param {Array} n 新数组
     * @param {Array} o 旧数组
     * @return: {Array} 新增数组
     */
export function getAddData(n, o) {
  if (!n || n.length < 1) {
    return []
  }
  if (!o || o.length < 1) {
    return n
  }
  return n.filter((e) => {
    return !o.includes(e)
  })
}

/**
 * @description: 解析字符串（数字）数组中减少的内容
 * @param {Array} n 新数组
 * @param {Array} o 旧数组
 * @return: {Array} 减少数组
 */
export function getDeleteData(n, o) {
  if (!o || o.length < 1) {
    return []
  }
  if (!n || n.length < 1) {
    return o
  }
  return o.filter((e) => {
    return !n.includes(e)
  })
}

/**
 * @description: 数组去重
 * @param {Array} arr 原数组
 * @return: {Array}
 */
export function unique(arr) {
  return Array.from(new Set(arr))
}

export function getEnvronment() {
  const isFeishu = window.__Lark_Bridge__ || window.lark;
  var ua = window.navigator.userAgent.toLowerCase();
  const isWechat = ua.match(/micromessenger/i) == 'micromessenger'
  return {isFeishu, isWechat}
}

export function isMimeMatch(ext, mimeTypes) {
  // 获取标准的MIME类型
  const fileMime = getMimeType(ext);

  return mimeTypes.some(mime => {
    // 处理通配符类型（如image/*）
    if (mime.endsWith('/*')) {
      const baseType = mime.replace('/*', '');
      return fileMime.startsWith(baseType);
    }

    // 精确匹配或类型/子类型匹配
    return mime === fileMime || mime === '*' || mime === `${fileMime.split('/')[0]}/*`;
  });
}
export function getMimeType(filename) {
  const ext = filename.split('.').pop().toLowerCase();

  const mimeMap = {
    // 图片类型
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    bmp: 'image/bmp',
    webp: 'image/webp',
    svg: 'image/svg+xml',

    // 文档类型
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    txt: 'text/plain',

    // 压缩文件
    zip: 'application/zip',
    rar: 'application/x-rar-compressed',
    '7z': 'application/x-7z-compressed',

    // 音频视频
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    mp4: 'video/mp4',
    mov: 'video/quicktime'
  };

  return mimeMap[ext] || 'application/octet-stream';
}

// 兼容部分iOS设备时间格式问题
export function getTimeStr(timeStr) {
  if (!timeStr) {
    return ''
  }
  if (Number.isNaN(new Date(timeStr).getTime())) {
    return timeStr.replace(/-/g, '/')
  } else {
    return timeStr
  }
}

// 获取文件尾缀
export function getFileSuffix(filename) {
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === filename.length - 1 || lastDotIndex === 0) {
    return '';
  }
  return filename.slice(lastDotIndex + 1);
}

