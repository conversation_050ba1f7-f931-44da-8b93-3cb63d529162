import axios from 'axios'
import { Toast } from 'fawkes-mobile-lib'
import { Message } from 'fawkes-lib'

import { requestEncrypt, responseEncrypt } from '@/config'
import { sign, cMsg } from '@/config/request'
import { encrypt, decrypt } from '@/utils/request/encrypt'
import { verifyRequest, verifyResponse } from './verify'
import { getSign } from '@/utils/request/sign'
import storage from '@/utils/storage'
import router from '@/router'
import { verifyMonitorUrl } from '@/utils/monitor'
import getFawkesBiz from './getFawkesBiz'
import store from '@/store'
import * as StateTypes from '@/store/State/stateTypes'
import {app_id} from "@utils/feishuapi";
//检查配置列表中是否包含当前url
const verify = function (config, arr) {
  return arr.find((item) => {
    return item == config.url
  })
}
function isMobile() {
  return /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
}
const Warning = isMobile() ? Toast : Message;
// 创建 axios 实例
const instance = axios.create({
  baseURL: process.env.VUE_APP_BASE_URL, // api base_url
  timeout: 50000, // 请求超时时间
  withCredentials: true,
  // 自定义参数序列化逻辑
  paramsSerializer: (params) => {
    const parts = [];
    Object.keys(params).forEach(key => {
      let val = params[key];
      if (val === null || typeof val === 'undefined') return;
      if (Array.isArray(val)) {
        const arrayKey = key + '[]';
        val.forEach(item => {
          parts.push(`${encodeURIComponent(arrayKey)}=${encodeURIComponent(item)}`);
        });
      } else {
        parts.push(`${encodeURIComponent(key)}=${encodeURIComponent(val)}`);
      }
    });
    return parts.join('&');
  }
})

instance.interceptors.request.use(
  (config) => {
    config.headers['Fawkes-Biz'] = getFawkesBiz(config) //接口统一携带'Fawkes-Biz'，用于处理接口的数据权限
    // 接口带vd的默认添加上projectId和projectName参数
    if (config.url.includes('/vehicle-dispatch/vd/')) {
      if (!config.params) {
        config.params = {};
      }
      let params = {}
      // 如果参数已经带了projectId和projectName, 那么就不需要处理
      if (!(config.params.projectId && config.params.projectName)) {
        if (router.currentRoute.path.includes('/projectCar/companyPortal/companyIndex') || router.currentRoute.path.includes('/projectCar/companyPortal/companyTodo')) {
          params = store.state.projectInfo
        } else {
          if (router.currentRoute.path.includes('projectPortal')) {
            params = {
              projectId: store.state.portal.id,
              projectName: store.state.portal.name
            };
          }
        }
      }
      config.params = {...config.params, ...params}
    }
    verify(config, sign)
      ? (config.params = getSign(config.params))
      : (config.headers['Fawkes-Auth'] = `${storage.get('access_token')}`)

    !!requestEncrypt && verifyRequest(config) && (config = encrypt(config))

    config = verifyMonitorUrl(config)

    return config
  },
  (error) => Promise.reject(error)
)

instance.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    !!responseEncrypt && verifyResponse(response) && (response = decrypt(response))

    let res = response.data
    const isValid = verify(response.config, cMsg);
    if (isValid) {
      return res
    }
    // console.log('8000170', response)
    // console.info('🚀🚀', 'response -->', response, `<-- index.js/`)
    // const whiteList = ['/vehicle-dispatch/vd/auth/login']
    // const isInWhiteList = whiteList.findIndex(item => response.config.url === item) > -1;
    // console.info('🚀🚀', 'isInWhiteList -->', isInWhiteList, `<-- index.js/`)
    // if (isInWhiteList) {
    //   return res;
    // }
    if (res.code === -8000170) {
      // storage.set('access_token', '');
      // storage.set('refresh_token', '');
      // if (store.state[StateTypes.APPLY_RESOURCE] === 3) {
      //   router.push({ path: '/login' }).catch((err) => err)
      // } else {
      //   router.push('/').catch(err => err)
      // }
      window.location.href = `https://open.feishu.cn/open-apis/authen/v1/index?app_id=${app_id}&redirect_uri=${window.encodeURIComponent(window.location.href.split('?')[0].split('#')[0])}`

      return Promise.reject(response)
    } else if (res.code === -8500041) {
      router.replace({ path: '/404', query: { errorMessage: res.message } })
    }

    if (!res.status && res.code) {
      Warning({
        message: res.message || '系统异常',
        type: 'error',
        duration: 5 * 1000,
        customClass: 'z-index-top'
      })
    }
    // if the custom code is not 20000, it is judged as an error.
    if (response.data instanceof Blob) {
      // 文件名在响应头中，所以需要将它传出去
      res.headers = response.headers;
    }
    return res
  },
  (error) => {
    // let message = ''
    console.info('🚀🚀', 'error -->', error, `<-- index.js/`)
    if (error.response) {
      switch (error.response.status) {
        case 400:
          Toast({
            message: error.response.data,
            type: 'error',
            duration: 5 * 1000,
            customClass: 'z-index-top'
          })
          break
        case 401:
          switch (error.response.data.code) {
            case -8000140:
            case -8000170:
            case -8000230:
              {
                storage.set('access_token', '');
                storage.set('refresh_token', '');
                if (store.state[StateTypes.APPLY_RESOURCE] === 3) {
                  router.push({ path: '/login' }).catch((err) => err)
                } else {
                  console.log('yyyyyyyyyyyyyyyyy')
                  router.push({path: '/', query: {reLogin: 'true'}}).catch(err => err)
                }
              }
              break
            case -8000160:
              {
                Toast({
                  message: '无证书提示',
                  type: 'error',
                  duration: 5 * 1000,
                })
                // conformHandler('无证书提示', error.response.data.message, action => {
                //   router.push({ path: '/uploadPermission' })
                // })
              }
              break
            case -8000130:
              {
                Warning({
                  message: error.response.data.message + '，请检查系统时间是否为最新时间',
                  type: 'error',
                  duration: 5 * 1000,
                })
                // conformHandler('无权限提示', error.response.data.message, action => {
                //   router.push({ path: '/uploadPermission' })
                // })
              }
              break
            default:
              Toast({
                message: error.response.data.message,
                type: 'error',
                duration: 5 * 1000,
                customClass: 'z-index-top'
              })
          }
          break
        case 403:
          Toast({
            message: '拒绝访问',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 404:
          Toast({
            message: '请求错误,未找到该资源',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 405:
          Toast({
            message: '请求方法未允许',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 408:
          Toast({
            message: '请求超时',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 500:
          Toast({
            message: '网络异常',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 501:
          Toast({
            message: '网络未实现',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 502:
          Toast({
            message: '网络错误',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 503:
          Toast({
            message: '服务不可用',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 504:
          Toast({
            message: '网络超时',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        case 505:
          Toast({
            message: 'http版本不支持该请求',
            type: 'error',
            duration: 5 * 1000,
          })
          break
        default:
          Toast({
            message: `连接错误${error.response.status}`,
            type: 'error',
            duration: 5 * 1000,
          })
      }
      if (error.response.config && verify(error.response.config, cMsg)) {
        // message = null
      }
    } else {
    }
    // message && resetMessage.error(message)
    if (error.code === 'ENCONNABORTED' || error.message.includes('timeout')) {
      Warning({
        message: '请求超时',
        type: 'error',
        duration: 3 * 1000,
        customClass: 'z-index-top'
      })
    }
    return Promise.reject(error)
  }
)

export default instance
