import {
  authRegister,
  authLogin
} from '@/api/carApply.js'

import { SET_OPEN_ID, SET_USER_LOCAL_INFO, SET_USER_INFO } from '@/store/Mutation/mutationTypes'
import { LOGIN_SUCCESS } from "@store/Action/actionTypes";
import storage from './storage'
import store from '@/store'
import * as StateTypes from '@/store/State/stateTypes'
import { Toast } from 'fawkes-mobile-lib'
const appid = 'wx5a1230c3ecb8c34b' // 测试
// const appid = 'wx5a99b0a9cddd14b7' // 线上
let redirect_uri = '';
let state = 'start'
const router = require('@/router').default

function wechatOfficialAccountInIt() {
  if (getRequest().state && getRequest().state.includes('loginAndRegister')) {
    const arrParams = getRequest().state.split('-');
    const params = {
      code: getRequest().code,
      phone: arrParams[2],
      source: store.state[StateTypes.APPLY_RESOURCE],
      captchaCode: arrParams[3],
      captchaKey: arrParams[4]
    }
    register(params).then(res => {
      console.log(arrParams[0], res)
      // state = 'login-' + arrParams[1]
      // getCode(appid, urleNcode(window.location.origin));
    })
    return false;
  }

  if (getRequest().state && getRequest().state.includes('login')) {
    storage.set('wxCode' ,getRequest().code);
    login();
    return false;
  }

  if (getRequest().state && getRequest().state.includes('register')){
    router.push('/login');
    return false;
  }

  // if (!!storage.get('access_token')) {
  //   go();
  //   return false;
  // }

  if (!!getRequest().code && getRequest().state === 'start') {
    storage.set('wxCode' ,getRequest().code);
    login();
    return false;
  }


  // let userInfo = JSON.parse(storage.get('userInfo'));  // 获取本地用户缓存(封装)
  // if (!userInfo) {
  //   // 判断用户是否已登录
  //   getCodeInfo();
  //   return;
  // }
  // getCodeInfo();
  console.log('getRegisterVerify======', location)
  getRegisterVerify();
  // go();  // 跳转登录页（封装）
}

// 获取code
function getCodeInfo() {
  redirect_uri = window.location.origin; // 微信api请求回调地址
  getCode(); // 微信api获取code
}

// 获取url的参数
function getRequest() {
  const url = location.search; //获取url中"?"符后的字串
  const theRequest = new Object();
  if (url.indexOf("?") != -1) {
    const str = url.substr(1);
    const strs = str.split("&");
    for(let i = 0; i < strs.length; i ++) {
      theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);
    }
  }
  return theRequest;
}

// urleNcode转码 防止url乱码
function urleNcode (str) {
  str = (str + '').toString();
  return encodeURIComponent(str).replace(/!/g, '%21').replace(/'/g, '%27').replace(/\(/g, '%28').
  replace(/\)/g, '%29').replace(/\*/g, '%2A').replace(/%20/g, '+');
}

// 获取code
function getCode() {
  const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${urleNcode(window.location.origin)}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
  // const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${urleNcode('http://daminproxy.hdec.com')}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
  // window.location.replace(url);
}

function getRegisterVerify() {
  const openId = getRequest().openId;
  // const openId = location.pathname.split('/')[2];
  store.dispatch('CarApply/getRegisterVerify', { openId, source: 2 }).then(registerVerify => {
    console.log('验证是否注册',getRequest(), registerVerify)
    if (!registerVerify.data) {
      // state = 'register-' + res.data.sysThirdParty.uuid + '-' +  res.data.sysThirdParty.userFullname;
      state = 'register-' + openId;
      // getCode();
      router.push({
        path: '/login',
        // query: { openId }
      });
      return false;
    } else {
      // todo 验证用户账户是否停用
      login()
    }
  }).catch(e => {
    console.log('验证是否注册失败', e)
  })
}
function login() {
  const openId = getRequest().openId;
  // const openId = location.pathname.split('/')[2];
  const params = {
    // code: getRequest().code,
    openId,
    source: store.state[StateTypes.APPLY_RESOURCE]
  }
  authLogin(params).then(async res => {
    if (!res.status) {
      Toast(res.message);
      router.push({
        path: '/404',
        query: { errorMessage: '登录失败' }
      });
      return false;
    }

    store.commit(SET_OPEN_ID,  openId);
    // store.commit(SET_OPEN_ID,  res.data.sysThirdParty.uuid);
    store.commit(SET_USER_LOCAL_INFO, res.data)
    store.commit(SET_USER_INFO, res.data)

    // 验证是否注册
    // const registerVerify = await store.dispatch('CarApply/getRegisterVerify', res.data.sysThirdParty.uuid);
    // if (!registerVerify.data) {
    //   // state = 'register-' + res.data.sysThirdParty.uuid + '-' +  res.data.sysThirdParty.userFullname;
    //   state = 'register-' + res.data.sysThirdParty.uuid;
    //   // getCode();
    //   router.push({
    //     path: '/login',
    //     query: { openId:  res.data.sysThirdParty.uuid }
    //   });
    //   return false;
    // }

    store.dispatch(LOGIN_SUCCESS, res.data)
  }).catch(e => {
    router.push({
      path: '/404',
      query: { errorMessage: '登录失败' }
    });
  }).finally(() => {
  });
}

function register(params) {
  return new Promise((resolve, reject) => {
    authRegister(params).then(async res => {
      if (!res.data && res.code === -8500010) {
        Toast(res.message);
        resolve(res)
        return false
      }
      if (!res.status) {
        Toast(res.message);
        resolve(res);
        return false;
      }
      state = 'login-' + params.openId
      // getCode();
      login();
      resolve(res);
    }).catch(e => {
      reject(e)
    }).finally(() => {
    })
  });
}

function go() {
  router.replace('/DispatchCar/carApply')
}

export {
  wechatOfficialAccountInIt,
  getRequest,
  register
}
