/*
 * @Author: your name
 * @Date: 2021-07-08 15:11:00
 * @LastEditTime: 2022-08-08 09:59:03
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \central-system\src\permission.js
 */
import router from './router'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import store from '@/store'
import {localRoute} from '@/config'
import {PERMISSIONS} from '@/store/Getter/getterTypes'
import {UPDATE_PERMISSION_QUEUE, SET_FEISHU_LINK} from '@/store/Mutation/mutationTypes'
import {FEISHU_LINK, PERMISSION} from '@/store/State/stateTypes'
import storage from '@/utils/storage'
import {Toast} from 'fawkes-mobile-lib'
import Vue from 'vue'

import {getUserRoutes} from "@utils/route";
import {getEnvronment} from "@utils/util";
function include(list, path) {
  return list.some(item => path.includes(item))
}

NProgress.configure({showSpinner: false}) // NProgress Configuration
function removeLoading() {
  let loading = document.getElementById('main-loading')
  loading && loading.parentNode.removeChild(loading)
}

router.beforeEach(async (to, from, next) => {
  if (!(to.path === '/' && from.path === '/')) {
    NProgress.start()
  }
  // console.info('🚀🚀', 'from -->', from, `<-- permission.js/`)
  // console.info('🚀🚀', 'to -->', to, `<-- permission.js/`)
  // 如果路由跳转到查看，处理，详情页，则不清空筛选器
  const toList = ['/tripDetail', '/flowCenter/userTask/form']
  const fromList = ['/DispatchCar/carApply', '/statistics/car', '/statistics/driver', '/department']
  const go = include(fromList, from.path) && include(toList, to.path);
  const back = include(fromList, to.path) && include(toList, from.path);
  if (!go && !back) {
    // 清空筛选器
    store.commit('SET_FILTERS', {type: 'clear'})
  }
  /* 路由守卫要做的事情
  * 1. 如果from是/, to不是/login和/sign,那么就显示骨架屏
  * 2. 开发环境下且不在飞书内,允许刷新获取路由;生产环境禁用
  * */
  const noAuthPaths = ['/projectCar/details', 'xcck', 'regis'];
  if (noAuthPaths.some(path => to.path.includes(path))) {
    // 详情页无需登录校验
    next();
  } else {
    const isLogin = storage.get('access_token');
    // 判断用户是否登录
    if (isLogin) {
      // 如果路由已加载, 则放行
      if (store.state.routes.length > 0) {
        // 当从飞书卡片跳转过来时，将路由信息
        if (from.path === '/' && to.path === '/projectCar/projectPortal/application' && Object.keys(to.query).length > 0) {
          console.info('🚀🚀', '已登录：飞书卡片 to -->', to, `<-- permission.js/`)
          // store.commit('SET_FEISHU_LINK', to)
          storage.set('feishu_link', JSON.stringify(to))
        }
        next()
      } else {
        // 加载线上路由，如果在登录页就不获取
        await getUserRoutes()
        next({...to})
      }
    } else {
      const {isFeishu, isWechat} = getEnvronment();
      if (!isFeishu && !isWechat) {
        // 去登录页, 避免爆栈
        if (to.path === '/login') {
          next()
        } else {
          next('/login')
        }
      } else {
        if (from.path === '/' && to.path !== '/') {
          // 当从飞书卡片跳转过来时，将路由信息
          if (from.path === '/' && to.path === '/projectCar/projectPortal/application' && Object.keys(to.query).length > 0) {
            console.info('🚀🚀', '未登录：飞书卡片 to -->', to, `<-- permission.js/`)
            // store.commit('SET_FEISHU_LINK', to)
            to.meta.query = to.query
            storage.set('feishu_link', JSON.stringify(to))
          }
          if (from.path === '/' && (to.path === '/sign' || to.path === '/login')) {
            next();
          } else {
            console.log(1111)
            next('/')
          }
        } else {
          if (to.path === '/') {
            next()
          } else {
            console.log(2222)
            next({path: '/', query: {reLogin: 'true'}})
          }
        }
      }
    }
  }

})
// router.beforeEach(async (to, from, next) => {
//     // start progress bar
//     NProgress.start()
//
//     let modeName = 'normal'
//
//     // switch (true) {
//     //     case Vue.prototype.$InQianKun: {
//     //         modeName = 'inqiankun'
//     //         break
//     //     }
//     //     case subApp: {
//     //         modeName = 'subapp'
//     //         break
//     //     }
//     //     case mainApp: {
//     //         modeName = 'mainapp'
//     //         break
//     //     }
//     //
//     //     default:
//     // }
//     let Guards = guardsList.getGuard('mainapp', to, from, next)
//
//     const redirect = Guards.routeRedirect()
//     if (!Guards.routeRedirect()) {
//         Guards.routeResolve()
//     }
// })

router.beforeResolve(async (to, from, next) => {
  // NProgress.start()
  if (localRoute || !to.meta || !to.meta.menuId || store.state[PERMISSION][to.meta.menuId]) {
    next()
    return false
  }
  next()
})

router.afterEach((to, from) => {
  let prefix = Vue.prototype.$appBasePath
  if (to.path == prefix + '/distribute') {
    setTimeout(() => {
      router.replace(prefix + store.getters.defaultRoute)  //多门户切换时，重定向到当前门户指定的默认路由
    }, 1000)
  }

  let redirect = storage.get('redirectUrl')

  if (redirect == to.fullPath) {
    storage.remove('redirectUrl')
  }

  // finish progress bar
  NProgress.done()
  removeLoading()
})

router.onError((error) => {
  Toast.error('加载的页面发生异常')

  console.error(error)
  NProgress.done()
  removeLoading()
})

export default router

/**
 * @description: 通过code查询用户权限是否已授权
 * @param {String} code 权限编码
 * @param {Object} params 鉴权参数  {Boolean} notFlag 反查标识 | {Array} types 作用门户等级 | {Boolean} entire 全量
 * @return: Boolean
 */
export function nodePermission(codes = [], params = {}) {
  //子应用适配
  let currentStore = Vue.prototype.$InQianKun ? Vue.prototype.$fksMainStore : store
  if (!currentStore) return false

  if (params?.types && currentStore.getters.multiPortal && !params.types.find(type => type == currentStore.state.portal.type)) {
    return true
  }

  let checkCode = (attr) => {
    return currentStore.getters[PERMISSIONS].find(function (item) {
      return attr && attr == item.code
    })
  }

  let findValue

  if (codes instanceof Array) {
    if (params?.entire) {
      findValue = codes.every(code => {
        return code && checkCode(code)
      })
    } else {
      findValue = codes.some(code => {
        return code && checkCode(code)
      })
    }
  } else {
    findValue = checkCode(codes)
  }

  checkCode = null

  const rvalue = !!findValue
  return localRoute ? true : (params?.notFlag ? !rvalue : rvalue)
}

export const dataPermission = (code, types) => {
  //子应用适配
  let currentStore = Vue.prototype.$InQianKun ? Vue.prototype.$fksMainStore : store
  if (!currentStore) {
    return false
  }

  if (types && currentStore.getters.multiPortal && !types.find(type => type == currentStore.state.portal.type)) {
    return true
  }

  let has = currentStore.getters[PERMISSIONS].find(p => p.code == code)
  has && currentStore.commit(UPDATE_PERMISSION_QUEUE, has)
  return !!has
}

export const redirectTo = (url) => {
  let to = '/home'
  if (url) {
    to = url
  }
  if (storage.get('redirect') == 1) {
    to = storage.get('redirectUrl') ? storage.get('redirectUrl') : '/home'
    storage.remove('redirect')
  }
  location.replace(location.href.replace(router.currentRoute.path, to))
}
